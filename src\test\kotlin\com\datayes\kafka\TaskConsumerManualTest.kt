package com.datayes.kafka

import com.datayes.domain.RuleInfo
import com.datayes.domain.TaskMessage
import com.datayes.spring.kafka.TaskConsumer
import org.junit.jupiter.api.Disabled
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.kafka.support.Acknowledgment
import java.util.UUID
import kotlin.test.Test

@SpringBootTest
class TaskConsumerManualTest {

    @Autowired
    lateinit var taskConsumer: TaskConsumer

    // 创建模拟 acknowledgment 的帮助方法 (Helper method to create mock acknowledgment)
    private fun createMockAcknowledgment() = Acknowledgment {
        // 测试时不需要真正的消息确认 (No real message acknowledgment needed for tests)
    }

    @Test
    @Disabled
    fun `should not error when send a empty message without any rules`() {

        // given
        val taskMessage = TaskMessage(
            taskId = 1,
            taskName = "test",
            rules = emptyList(),
            postTasks = emptyList(),
            runId = "1",
            operator = "SYSTEM"
        )

        // when
        taskConsumer.onMessage(taskMessage, createMockAcknowledgment())

        // then
    }

    // test with simple sql: select 1
    @Test
    @Disabled
    fun `should not error when send a simple sql`() {
        // given
        val taskMessage = TaskMessage(
            taskId = 3,
            taskName = "test",
            rules = listOf(
                RuleInfo(
                    ruleId = 2,
                    ruleName = "test",
                    dataSource = "2",
                    ruleSql = "SELECT appno, policyno, gpflag  FROM view_contidt_000001 where policyno > '86110020240210011330'",
                    countSql = "SELECT COUNT(*) FROM view_contidt_000001",
                    businessKeyList = listOf("policyno", "appno")
                )
            ),
            postTasks = emptyList(),
            runId = UUID.randomUUID().toString(),
            operator = "SYSTEM"
        )

        // when
        taskConsumer.onMessage(taskMessage, createMockAcknowledgment())

        // then
    }

    @Test
    @Disabled
    fun `should validate beneficiary number not empty rule`() {
        // given
        val taskMessage = TaskMessage(
            taskId = 3,
            taskName = "test",
            rules = listOf(
                RuleInfo(
                    ruleId = 2,
                    ruleName = "受益人序号不能为空",
                    dataSource = "1",
                    ruleSql = "select * from full_lcbnf",
                    countSql = "",
                    businessKeyList = emptyList(),
                    databaseName = "URP_DWS",
                    relatedConfig = mapOf(
                        "allColumnNotEmpty" to 0,
                        "checkField" to "bnfno",
                        "configList" to emptyList<Any>(),
                        "whereCondition" to ""
                    )
                )
            ),
            postTasks = emptyList(),
            // runId = "558dd343-202b-4d1d-884e-248881a8b683",
            runId = UUID.randomUUID().toString(),
            operator = "SYSTEM"
        )

        // when
        taskConsumer.onMessage(taskMessage, createMockAcknowledgment())

        // then
        // Verification can be added based on expected behavior
    }
}
