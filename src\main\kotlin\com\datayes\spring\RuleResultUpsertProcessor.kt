package com.datayes.spring

import com.datayes.domain.RuleResultEntity
import com.datayes.executor.ResultBatchProcessor
import org.springframework.stereotype.Service
import org.springframework.jdbc.core.JdbcTemplate


@Service
class RuleResultUpsertProcessor(private val jdbcTemplate: JdbcTemplate) : ResultBatchProcessor {

    private val log = org.slf4j.LoggerFactory.getLogger(RuleResultUpsertProcessor::class.java)

    companion object {
        private const val UPSERT_SQL = """
            INSERT INTO rule_execution_results (
                RULE_ID, DB_TYPE, ORIGINAL_SQL, RUN_ID, EXECUTED_AT,
                ROW_DATA, BUSINESS_KEY_DATA, BUSINESS_KEY_DATA_MD5,
                ROW_INDEX, EXECUTION_TIME_MS, IS_FIXED, CREATED_BY, CREATED_AT
            ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
            ON DUPLICATE KEY UPDATE
                RUN_ID = VALUES(RUN_ID),
                IS_FIXED = 0,
                UPDATED_AT = NOW()
        """
    }

    override fun process(batch: List<RuleResultEntity>) {
        if (batch.isEmpty()) return

        val params = batch.map { result ->
            arrayOf(
                result.ruleId,
                result.dbType,
                result.originalSql,
                result.runId,
                result.executedAt,
                result.rowData,
                result.businessKeyData,
                result.businessKeyDataMd5,
                result.rowIndex,
                result.executionTimeMs,
                result.isFixed,
                result.createdBy,
                result.createdAt
            )
        }

        jdbcTemplate.batchUpdate(UPSERT_SQL, params)
    }
}