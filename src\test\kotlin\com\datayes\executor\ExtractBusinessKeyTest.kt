package com.datayes.executor

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class ExtractBusinessKeyTest {

    @Test
    fun `should return original row when bk is empty`() {
        // given
        val row = mapOf("name" to "1")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, emptyList())

        // then
        expectThat(ret).isEqualTo(row)
    }

    @Test
    fun `should return only specified business keys`() {
        // given
        val row = mapOf(
            "id" to 100,
            "name" to "张三",
            "age" to 30,
            "address" to "北京"
        )
        val businessKeyList = listOf("id", "name")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, businessKeyList)

        // then
        expectThat(ret).isEqualTo(mapOf(
            "id" to 100,
            "name" to "张三"
        ))
    }

    @Test
    fun `should handle keys not present in the row`() {
        // given
        val row = mapOf(
            "id" to 100,
            "name" to "张三"
        )
        val businessKeyList = listOf("id", "non_existent_key")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, businessKeyList)

        // then
        expectThat(ret).isEqualTo(mapOf(
            "id" to 100,
            "non_existent_key" to null
        ))
    }

    @Test
    fun `should handle empty row`() {
        // given
        val row = emptyMap<String, Any?>()
        val businessKeyList = listOf("id", "name")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, businessKeyList)

        // then
        expectThat(ret).isEqualTo(mapOf(
            "id" to null,
            "name" to null
        ))
    }

    @Test
    fun `should handle row with null values`() {
        // given
        val row = mapOf(
            "id" to 100,
            "name" to null,
            "age" to 30
        )
        val businessKeyList = listOf("id", "name")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, businessKeyList)

        // then
        expectThat(ret).isEqualTo(mapOf(
            "id" to 100,
            "name" to null
        ))
    }

    @Test
    fun `should handle various data types`() {
        // given
        val row = mapOf(
            "string_key" to "字符串",
            "int_key" to 123,
            "double_key" to 123.45,
            "boolean_key" to true,
            "list_key" to listOf(1, 2, 3),
            "map_key" to mapOf("a" to 1, "b" to 2)
        )
        val businessKeyList = listOf("string_key", "int_key", "double_key", "boolean_key", "list_key", "map_key")

        // when
        val ret = DatabaseExecutor.extractBusinessKey(row, businessKeyList)

        // then
        expectThat(ret).isEqualTo(row)
    }
}