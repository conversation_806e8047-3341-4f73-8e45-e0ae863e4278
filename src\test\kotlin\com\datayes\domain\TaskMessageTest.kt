package com.datayes.domain

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class TaskMessageTest {

    private val mapper = jacksonObjectMapper()
        .registerKotlinModule()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    @Test
    fun `should deserialize JSON to TaskMessage without exception`() {
        val json = """
            {
              "postTasks": [
                {
                  "rules": [
                    {
                      "ruleId": 2,
                      "dataSource": "2",
                      "ruleName": "更新后的规则名称",
                      "ruleSql": "select RULE_LEVEL,PARENT_ID from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                      "countSql": "select count(*) from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                      "businessKeyList": ["RULE_LEVEL", "PARENT_ID"]
                    },
                    {
                      "ruleId": 2,
                      "dataSource": "2",
                      "ruleName": "规则2",
                      "ruleSql": "select RULE_LEVEL,PARENT_ID from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                      "countSql": "select count(*) from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                      "businessKeyList": ["RULE_LEVEL", "PARENT_ID"]
                    }
                  ],
                  "taskId": 3,
                  "taskName": "测试任务"
                }
              ],
              "rules": [
                {
                  "ruleId": 2,
                  "dataSource": "2",
                  "ruleName": "更新后的规则名称",
                  "ruleSql": "select RULE_LEVEL,PARENT_ID from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                  "countSql": "select count(*) from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                  "businessKeyList": ["RULE_LEVEL", "PARENT_ID"]
                },
                {
                  "ruleId": 2,
                  "ruleName": "规则2",
                  "dataSource": "2",
                  "ruleSql": "select RULE_LEVEL,PARENT_ID from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                  "countSql": "select count(*) from RULE_TYPE a where a.QA_ACTIVE_FLG=1 group by RULE_LEVEL,PARENT_ID having count(1)>1 ",
                  "businessKeyList": ["RULE_LEVEL", "PARENT_ID"]
                }
              ],
              "taskId": 2,
              "taskName": "测试任务",
              "runId": "934",
              "operator": "system"
            }
        """.trimIndent()

        val taskMessage = mapper.readValue(json, TaskMessage::class.java)

        // 验证反序列化后的对象
        assertEquals(2, taskMessage.taskId)
        assertEquals("测试任务", taskMessage.taskName)
        assertEquals("934", taskMessage.runId)
        assertEquals("system", taskMessage.operator)
        
        // 验证 postTasks
        assertEquals(1, taskMessage.postTasks.size)
        with(taskMessage.postTasks[0]) {
            assertEquals(3, taskId)
            assertEquals("测试任务", taskName)
            assertEquals(2, rules.size)
        }

        // 验证主规则列表
        assertEquals(2, taskMessage.rules.size)
        with(taskMessage.rules[0]) {
            assertEquals(2, ruleId)
            assertEquals("更新后的规则名称", ruleName)
        }
    }

    @Test
    fun `should deserialize complex task message with real business rules`() {
        val json = """
            {
              "operator":"admin",
              "postTasks":[],
              "rules":[
                {
                  "businessKeyList":[],
                  "countSql":"SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (1=1)",
                  "dataSource":"5",
                  "databaseName":"usp_east",
                  "ruleId":36,
                  "ruleName":"测试-数据重复检查1",
                  "ruleSql":"SELECT StartDate,AgencyCode,AgtCode FROM (SELECT a.StartDate,a.AgencyCode,a.AgtCode FROM full_laagtcode1 as a WHERE (1=1)  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)) as temp GROUP BY StartDate,AgencyCode HAVING (count(1)>1)",
                  "tableAlias":"a",
                  "tableName":"full_laagtcode1",
                  "uniqueKey":"[{\"tableName\":\"full_laagtcode1\",\"fieldName\":\"AgtCode\"}]"
                },
                {
                  "businessKeyList":[],
                  "countSql":"SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))",
                  "dataSource":"5",
                  "databaseName":"usp_east",
                  "ruleId":35,
                  "ruleName":"测试-数据为空校验1",
                  "ruleSql":"SELECT a.AgencyCode,a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))  AND ((a.AgencyCode is null or a.AgencyCode ='') AND (a.AgencyCertNo is null or a.AgencyCertNo =''))  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)",
                  "tableAlias":"a",
                  "tableName":"full_laagtcode1",
                  "uniqueKey":"[{\"tableName\":\"full_laagtcode1\",\"fieldName\":\"AgtCode\"}]"
                }
              ],
              "runId":"64a14b68-62be-46a0-ab29-afa94a0a2a0b",
              "sendTime":"2025-05-23 10:53:16",
              "taskId":26
            }
        """.trimIndent()

        val taskMessage = mapper.readValue(json, TaskMessage::class.java)

        // 验证基本任务信息 (basic task information)
        assertEquals(26, taskMessage.taskId)
        assertEquals(null, taskMessage.taskName) // taskName 在 JSON 中不存在，应为 null
        assertEquals("64a14b68-62be-46a0-ab29-afa94a0a2a0b", taskMessage.runId)
        assertEquals("admin", taskMessage.operator)
        
        // 验证 postTasks 为空 (verify postTasks is empty)
        assertEquals(0, taskMessage.postTasks.size)

        // 验证主规则列表 (verify main rules list)
        assertEquals(2, taskMessage.rules.size)
        
        // 验证第一个规则的核心字段 (verify first rule's core fields)
        with(taskMessage.rules[0]) {
            assertEquals(36, ruleId)
            assertEquals("测试-数据重复检查1", ruleName)
            assertEquals("5", dataSource)
            assertEquals("usp_east", databaseName)
            assertEquals(0, businessKeyList!!.size) // 空的业务键列表 (empty business key list)
            assertEquals("SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (1=1)", countSql)
            assertEquals("SELECT StartDate,AgencyCode,AgtCode FROM (SELECT a.StartDate,a.AgencyCode,a.AgtCode FROM full_laagtcode1 as a WHERE (1=1)  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)) as temp GROUP BY StartDate,AgencyCode HAVING (count(1)>1)", ruleSql)
        }
        
        // 验证第二个规则的核心字段 (verify second rule's core fields)
        with(taskMessage.rules[1]) {
            assertEquals(35, ruleId)
            assertEquals("测试-数据为空校验1", ruleName)
            assertEquals("5", dataSource)
            assertEquals("usp_east", databaseName)
            assertEquals(0, businessKeyList!!.size) // 空的业务键列表 (empty business key list)
            assertEquals("SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))", countSql)
            assertEquals("SELECT a.AgencyCode,a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))  AND ((a.AgencyCode is null or a.AgencyCode ='') AND (a.AgencyCertNo is null or a.AgencyCertNo =''))  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)", ruleSql)
        }
    }

    @Test
    fun `should deserialize JSON with unknown properties like tableAlias without exception`() {
        val json = """
            {
              "postTasks": [],
              "operator": "admin",
              "rules": [
                {
                  "businessKeyList": [],
                  "countSql": "SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))",
                  "dataSource": "5",
                  "databaseName": "usp_east",
                  "ruleId": 35,
                  "ruleName": "测试-数据为空校验1",
                  "ruleSql": "SELECT a.AgencyCode,a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))  AND ((a.AgencyCode is null or a.AgencyCode ='') AND (a.AgencyCertNo is null or a.AgencyCertNo =''))  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)",
                  "tableAlias": "a",
                  "tableName": "full_laagtcode1",
                  "uniqueKey": "[{\"tableName\":\"full_laagtcode1\",\"fieldName\":\"AgtCode\"}]"
                }
              ],
              "runId": "64a14b68-62be-46a0-ab29-afa94a0a2a0b",
              "sendTime": "2025-05-23 10:53:16",
              "taskId": 26
            }
        """.trimIndent()

        val taskMessage = mapper.readValue(json, TaskMessage::class.java)

        // 验证基本任务信息 (basic task information)
        assertEquals(26, taskMessage.taskId)
        assertEquals(null, taskMessage.taskName) // taskName 在 JSON 中不存在，应为 null
        assertEquals("64a14b68-62be-46a0-ab29-afa94a0a2a0b", taskMessage.runId)
        assertEquals("admin", taskMessage.operator)
        
        // 验证 postTasks 为空 (verify postTasks is empty)
        assertEquals(0, taskMessage.postTasks.size)

        // 验证主规则列表 (verify main rules list)
        assertEquals(1, taskMessage.rules.size)
        
        // 验证第一个规则的核心字段 (verify first rule's core fields)
        with(taskMessage.rules[0]) {
            assertEquals(35, ruleId)
            assertEquals("测试-数据为空校验1", ruleName)
            assertEquals("5", dataSource)
            assertEquals("usp_east", databaseName)
            assertEquals(emptyList(), businessKeyList)
            // 注意：tableAlias、tableName、uniqueKey 这些字段应该被忽略，不会引起异常
        }
    }

    @Test
    fun `should deserialize JSON with unknown properties using older JSON structure`() {
        val json = """
            {
              "postTasks": [],
              "operator": "admin",
              "rules": [
                {
                  "businessKeyList": [],
                  "countSql": "SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))",
                  "dataSource": "5",
                  "databaseName": "usp_east",
                  "ruleId": 35,
                  "ruleName": "测试-数据为空校验1",
                  "ruleSql": "SELECT a.AgencyCode,a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))  AND ((a.AgencyCode is null or a.AgencyCode ='') AND (a.AgencyCertNo is null or a.AgencyCertNo =''))  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)",
                  "tableAlias": "a",
                  "tableName": "full_laagtcode1",
                  "uniqueKey": "[{\"tableName\":\"full_laagtcode1\",\"fieldName\":\"AgtCode\"}]"
                }
              ],
              "runId": "64a14b68-62be-46a0-ab29-afa94a0a2a0b",
              "sendTime": "2025-05-23 10:53:16",
              "taskId": 26
            }
        """.trimIndent()

        val taskMessage = mapper.readValue(json, TaskMessage::class.java)

        // 验证基本任务信息 (basic task information)
        assertEquals(26, taskMessage.taskId)
        assertEquals(null, taskMessage.taskName) // taskName 在 JSON 中不存在，应为 null
        assertEquals("64a14b68-62be-46a0-ab29-afa94a0a2a0b", taskMessage.runId)
        assertEquals("admin", taskMessage.operator)
        
        // 验证 postTasks 为空 (verify postTasks is empty)
        assertEquals(0, taskMessage.postTasks.size)

        // 验证主规则列表 (verify main rules list)
        assertEquals(1, taskMessage.rules.size)
        
        // 验证第一个规则的核心字段 (verify first rule's core fields)
        with(taskMessage.rules[0]) {
            assertEquals(35, ruleId)
            assertEquals("测试-数据为空校验1", ruleName)
            assertEquals("5", dataSource)
            assertEquals("usp_east", databaseName)
            assertEquals(emptyList(), businessKeyList)
            // 注意：tableAlias、tableName、uniqueKey 这些未知字段应该被忽略
        }
    }
}
