package com.datayes.domain

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

// Define ComparisonType enum
enum class ComparisonType {
    ABSOLUTE_DIFFERENCE, // abs(目标数据量 - 源数据量)
    TARGET_DIV_SOURCE_RATIO, // 目标数据量 / 源数据量
    SOURCE_DIV_TARGET_RATIO  // 源数据量 / 目标数据量
}

// Define RuleType enum
enum class RuleType {
    SQL_QUERY,           // SQL 查询规则
    COUNT_COMPARISON     // 计数比较规则
}

data class TaskMessage(
    val postTasks: List<TaskDetail>,
    val rules: List<RuleInfo>,
    val taskId: Long,
    val taskName: String?,
    val runId: String,
    val operator: String
)

data class TaskDetail(
    val rules: List<RuleInfo>,
    val taskId: Long,
    val taskName: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class RuleInfo(
    val ruleId: Long,
    val ruleName: String,

    // start of sql query rule
    val dataSource: String? = null,
    val ruleSql: String? = null,
    val countSql: String? = null,
    val businessKeyList: List<String>? = null,
    val databaseName: String? = null,
    // end of sql query rule

    // start of count comparison rule
    val sourceSql: String? = null,
    val targetSql: String? = null,
    val sourceDatabaseName: String? = null,
    val sourceDataSource: String? = null,
    val targetDatabaseName: String? = null,
    val targetDataSource: String? = null,
    val comparisonType: ComparisonType? = null,
    val threshold: Double? = null,
    // end of count comparison rule

    val relatedConfig: Map<String, Any?>? = null
)
