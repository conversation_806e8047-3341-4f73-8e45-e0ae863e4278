# 数据库 (ER Diagram)

```mermaid
erDiagram
    DATA_QUALITY_ISSUES {
        BIGINT id PK
        BIGINT rule_id
        BIGINT task_id
        VARCHAR status
        VARCHAR assigned_to
    }

    ISSUE_RUN_HISTORY {
        BIGINT id PK
        BIGINT issue_id FK
        BIGINT rule_id
        BIGINT task_id
        VARCHAR run_id
        DATETIME start_time
        DATETIME end_time
    }

    RULE_EXECUTION_RESULTS {
        BIGINT id PK
        BIGINT rule_id
        VARCHAR run_id FK
        DATETIME executed_at
        VARCHAR row_data
        INT is_fixed
    }

    RULE_EXECUTION_LOG {
        BIGINT id PK
        VARCHAR run_id FK
        BIGINT rule_id
        BIGINT task_id
        DATETIME timestamp
        VARCHAR level
        VARCHAR message
    }

    METADATA_DATA_SOURCE {
        BIGINT id PK
        VARCHAR source_name
        VARCHAR db_type
        VARCHAR db_name
        VARCHAR db_url
    }

    DATA_QUALITY_ISSUES ||--o{ ISSUE_RUN_HISTORY : contains
    ISSUE_RUN_HISTORY ||--o{ RULE_EXECUTION_RESULTS : generates
    ISSUE_RUN_HISTORY ||--o{ RULE_EXECUTION_LOG : logs
```
