package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.annotation.Version
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("data_quality_issues")
data class IssueEntity(

    @Id
    val id: Long? = null,

    @Column("RULE_ID")
    val ruleId: Long,

    @Column("TASK_ID")
    val taskId: Long,

    @Column("STATUS")
    val status: String? = null,

    @Column("ASSIGNED_TO")
    val assignedTo: String? = null,

    @Column("CURRENT_STAGE")
    val currentStage: IssueStage? = null,

    @Column("CREATE_BY")
    val createBy: String,

    @Column("CREATE_AT")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_AT")
    val updatedAt: LocalDateTime? = null,

    @Version
    @Column("VERSION")
    val version: Long = 0,
)

/**
 * 问题运行阶段枚举类，用于内部管理问题/规则的运行状态
 */
enum class IssueStage {

    /**
     * 首次创建，未执行
     */
    NOT_STARTED,

    /**
     * 等待运行，通常是通过 Kafka 消息触发，也用于重新运行的情况
     */
    WAITING,

    /**
     * 正在运行中
     */
    RUNNING,

    /**
     * 运行出错
     */
    ERROR,

    /**
     * 成功运行并得到结果
     */
    COMPLETED,

    /**
     * 工作节点崩溃
     */
    CRASHED
}

/**
 * Represents the execution history of a single issue run.
 */
@Table("issue_run_history")
data class IssueRunHistoryEntity(

    @Id
    val id: Long? = null,

    @Column("ISSUE_ID")
    val issueId: Long,

    @Column("TASK_ID")
    val taskId: Long,

    @Column("RULE_ID")
    val ruleId: Long,

    @Column("RUN_ID")
    val runId: String,

    @Column("START_TIME")
    val startTime: LocalDateTime,

    @Column("END_TIME")
    val endTime: LocalDateTime? = null,

    @Column("TOTAL_SCAN_COUNT")
    val totalScanCount: Long? = null,

    @Column("ERROR_ROW_COUNT")
    val errorRowCount: Int? = null,

    @Column("STATUS")
    val status: RunStatus,

    @Column("ERROR_MESSAGE")
    val errorMessage: String? = null
)

/**
 * Represents the status of a single issue run.
 */
enum class RunStatus {
    STARTED,
    COMPLETED,
    FAILED,
    TIMEOUT
}