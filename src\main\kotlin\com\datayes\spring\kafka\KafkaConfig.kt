package com.datayes.spring.kafka

import com.datayes.domain.CancelMessage
import com.datayes.domain.TaskMessage
import com.fasterxml.jackson.databind.ObjectMapper // 新增导入
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.serialization.StringDeserializer
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer
import org.springframework.kafka.listener.DefaultErrorHandler
import org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
import org.springframework.kafka.support.serializer.JsonDeserializer
import org.springframework.kafka.listener.ContainerProperties
import org.springframework.util.backoff.FixedBackOff

@Configuration
class KafkaConfig(
    private val kafkaProperties: KafkaProperties,
    private val kafkaTemplate: KafkaTemplate<String, String>,
    private val objectMapper: ObjectMapper // 注入 Spring Boot 配置的 ObjectMapper
) {

    private val log = org.slf4j.LoggerFactory.getLogger(KafkaConfig::class.java)

    @Bean
    fun cancelConsumerFactory(): ConsumerFactory<String, CancelMessage> {
        val props = kafkaProperties.buildConsumerProperties().toMutableMap()
        // 使用注入的 objectMapper 初始化 JsonDeserializer
        val valueDeserializer = JsonDeserializer(CancelMessage::class.java, objectMapper).apply {
            setRemoveTypeHeaders(false)
            addTrustedPackages("com.datayes.domain")
            setUseTypeMapperForKey(false)
            // 配置忽略未知属性 (configure to ignore unknown properties)
            setUseTypeHeaders(false)
            ignoreTypeHeaders()
        }
        return DefaultKafkaConsumerFactory(
            props,
            StringDeserializer(),
            ErrorHandlingDeserializer(valueDeserializer)
        )
    }

    @Bean
    fun taskConsumerFactory(): ConsumerFactory<String, TaskMessage> {
        val props = kafkaProperties.buildConsumerProperties().toMutableMap()
        // 使用注入的 objectMapper 初始化 JsonDeserializer
        val valueDeserializer = createTaskMessageDeserializer(objectMapper)
        return DefaultKafkaConsumerFactory(
            props,
            StringDeserializer(),
            ErrorHandlingDeserializer(valueDeserializer)
        )
    }

    @Bean
    fun cancelKafkaListenerContainerFactory(): ConcurrentKafkaListenerContainerFactory<String, CancelMessage> {
        val factory = ConcurrentKafkaListenerContainerFactory<String, CancelMessage>()
        factory.consumerFactory = cancelConsumerFactory()
        factory.setCommonErrorHandler(errorHandler())
        // 启用手动确认模式 (Enable manual acknowledgment mode)
        factory.containerProperties.ackMode = ContainerProperties.AckMode.MANUAL_IMMEDIATE
        return factory
    }

    @Bean
    fun taskKafkaListenerContainerFactory(): ConcurrentKafkaListenerContainerFactory<String, TaskMessage> {
        val factory = ConcurrentKafkaListenerContainerFactory<String, TaskMessage>()
        factory.consumerFactory = taskConsumerFactory()
        factory.setCommonErrorHandler(errorHandler())
        // 启用手动确认模式 (Enable manual acknowledgment mode)
        factory.containerProperties.ackMode = ContainerProperties.AckMode.MANUAL_IMMEDIATE
        return factory
    }

    @Bean
    fun errorHandler(): DefaultErrorHandler {
        // 创建专用于DLQ的KafkaTemplate，使用ByteArraySerializer而非StringSerializer
        val props = kafkaProperties.buildProducerProperties().toMutableMap()
        props["key.serializer"] = "org.apache.kafka.common.serialization.StringSerializer"
        props["value.serializer"] = "org.apache.kafka.common.serialization.ByteArraySerializer"

        val dlqKafkaTemplate = KafkaTemplate(
            DefaultKafkaProducerFactory<String, ByteArray>(props)
        )

        // 配置 DeadLetterPublishingRecoverer 以处理消息发送失败的情况
        val recoverer = DeadLetterPublishingRecoverer(
            dlqKafkaTemplate,
            // 使用函数式接口确定死信队列主题
            { record, ex ->
                // 发送到对应主题的DLQ
                val topicPartition = TopicPartition(record.topic() + ".DLQ", record.partition())
                log.error("0f3f6395 | 消息发送失败 (Message send failed): ${record.value()}", ex)
                topicPartition
            }
        ).apply {
            // 设置为 false，避免因发送错误而失败
            setFailIfSendResultIsError(false)
        }

        val errorHandler = DefaultErrorHandler(
            recoverer,
            FixedBackOff(0L, 1L) // 最大重试1次
        )

        // 添加不可重试的异常类型
        errorHandler.addNotRetryableExceptions(com.fasterxml.jackson.core.JsonProcessingException::class.java)
        errorHandler.addNotRetryableExceptions(org.apache.kafka.common.errors.RecordDeserializationException::class.java)
        errorHandler.addNotRetryableExceptions(org.apache.kafka.common.errors.SerializationException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.ClassCastException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.IllegalArgumentException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.NullPointerException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.NoSuchMethodException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.NoSuchFieldException::class.java)
        errorHandler.addNotRetryableExceptions(java.lang.IllegalStateException::class.java)

        return errorHandler
    }
}

fun createTaskMessageDeserializer(objectMapper: ObjectMapper): JsonDeserializer<TaskMessage> =
    JsonDeserializer(TaskMessage::class.java, objectMapper).apply {
        setRemoveTypeHeaders(false)
        addTrustedPackages("com.datayes.domain")
        setUseTypeMapperForKey(false)
        // 配置忽略未知属性 (configure to ignore unknown properties)
        setUseTypeHeaders(false)
        ignoreTypeHeaders()
    }
