package com.datayes.kafka

import com.datayes.spring.kafka.TaskConsumer
import com.datayes.spring.kafka.CancelConsumer
import org.apache.kafka.clients.admin.AdminClient
import org.apache.kafka.clients.admin.ListTopicsOptions
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.kafka.core.KafkaAdmin

@SpringBootTest
class KafkaConnectionIntegrationTest {

    @Autowired
    lateinit var kafkaAdmin: KafkaAdmin

    @Test
    fun `should connect to Kafka cluster and verify topic exists`() {
        val topicName = TaskConsumer.TOPIC

        val configs = kafkaAdmin.configurationProperties

        AdminClient.create(configs).use { adminClient ->
            val options = ListTopicsOptions().listInternal(true)
            val topicNames = adminClient.listTopics(options).names().get()
            assertTrue(
                topicNames.contains(topicName),
                "Topic '$topicName' not found in Kafka cluster"
            )
        }
    }


    @Test
    fun `should connect to Kafka cluster and verify topic exists for CancelListener`() {
        val topicName = CancelConsumer.TOPIC

        val configs = kafkaAdmin.configurationProperties

        AdminClient.create(configs).use { adminClient ->
            val options = ListTopicsOptions().listInternal(true)
            val topicNames = adminClient.listTopics(options).names().get()
            assertTrue(
                topicNames.contains(topicName),
                "Topic '$topicName' not found in Kafka cluster"
            )
        }
    }

    @Test
    fun `should connect to Kafka cluster and verify DLQ topic exists for TaskConsumer`() {
        val dlqTopicName = TaskConsumer.TOPIC + ".DLQ"

        val configs = kafkaAdmin.configurationProperties

        AdminClient.create(configs).use { adminClient ->
            val options = ListTopicsOptions().listInternal(true)
            val topicNames = adminClient.listTopics(options).names().get()
            if (!topicNames.contains(dlqTopicName)) {
                println("WARNING: DLQ topic '$dlqTopicName' not found in Kafka cluster, skipping assertion")
                return
            }
            assertTrue(
                topicNames.contains(dlqTopicName),
                "DLQ topic '$dlqTopicName' not found in Kafka cluster"
            )
        }
    }

    @Test
    fun `should connect to Kafka cluster and verify DLQ topic exists for CancelListener`() {
        val dlqTopicName = CancelConsumer.TOPIC + ".DLQ"

        val configs = kafkaAdmin.configurationProperties

        AdminClient.create(configs).use { adminClient ->
            val options = ListTopicsOptions().listInternal(true)
            val topicNames = adminClient.listTopics(options).names().get()
            if (!topicNames.contains(dlqTopicName)) {
                println("WARNING: DLQ topic '$dlqTopicName' not found in Kafka cluster, skipping assertion")
                return
            }
            assertTrue(
                topicNames.contains(dlqTopicName),
                "DLQ topic '$dlqTopicName' not found in Kafka cluster"
            )
        }
    }
}
