kind: Deployment
apiVersion: {{ include "app.deployment.apiVersion" . }}
metadata:
  name: {{ .Release.Name }}
  labels:
{{ include "service.labels.standard" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
{{ include "service.match.labels" . | indent 6 }}
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
{{ include "service.labels.standard" . | indent 8 }}
{{ include "service.microservice.labels" . | indent 8 }}
    spec:
{{- if .Values.hostAliases }}
      hostAliases:
{{ toYaml .Values.hostAliases | indent 8 }}
{{- end }}
      containers:
      - name: {{ .Release.Name }}
        image: "{{ .Values.image.repository }}:{{ .Chart.Version }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        env:
{{- range $name, $value := .Values.env.open }}
{{- if ne (len ($value | quote)) 0 }}
        - name: {{ $name | quote }}
          value: {{ $value | quote }}
{{- end }}
{{- end }}
        ports:
        - name: http
          containerPort: {{ .Values.config.port }}
          protocol: TCP
        livenessProbe:
          initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          httpGet:
            port: {{ .Values.env.open.SERVER_PORT }}
            scheme: HTTP
            path: {{ .Values.env.open.HEALTH_URL_liveness }}
        readinessProbe:
          initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          httpGet:
            port: {{ .Values.env.open.SERVER_PORT }}
            scheme: HTTP
            path: {{ .Values.env.open.HEALTH_URL_readiness }}
        resources:
{{ toYaml .Values.resources | indent 12 }}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
  revisionHistoryLimit: 3
  minReadySeconds: 0

