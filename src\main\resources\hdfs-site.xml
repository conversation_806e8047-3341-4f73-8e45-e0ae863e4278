<!--Sun May 25 05:54:44 2025-->
    <configuration>
    
    <property>
      <name>datanode.jmx.port</name>
      <value>8701</value>
    </property>
    
    <property>
      <name>dfs.balancer.dispatcherThreads</name>
      <value>50</value>
    </property>
    
    <property>
      <name>dfs.balancer.moverThreads</name>
      <value>5000</value>
    </property>
    
    <property>
      <name>dfs.block.access.token.enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.blockreport.incremental.intervalMsec</name>
      <value>500</value>
    </property>
    
    <property>
      <name>dfs.blockreport.initialDelay</name>
      <value>120</value>
    </property>
    
    <property>
      <name>dfs.blockreport.split.threshold</name>
      <value>500000</value>
    </property>
    
    <property>
      <name>dfs.blocksize</name>
      <value>134217728</value>
    </property>
    
    <property>
      <name>dfs.client.failover.proxy.provider.hdfsCluster</name>
      <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>
    
    <property>
      <name>dfs.client.read.shortcircuit</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.client.read.shortcircuit.streams.cache.size</name>
      <value>4096</value>
    </property>
    
    <property>
      <name>dfs.cluster.administrators</name>
      <value>hdfs</value>
    </property>
    
    <property>
      <name>dfs.datanode.address</name>
      <value>0.0.0.0:50010</value>
    </property>
    
    <property>
      <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-preference-fraction</name>
      <value>0.9</value>
    </property>
    
    <property>
      <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-threshold</name>
      <value>42949672960</value>
    </property>
    
    <property>
      <name>dfs.datanode.balance.bandwidthPerSec</name>
      <value>52428800</value>
    </property>
    
    <property>
      <name>dfs.datanode.balance.max.concurrent.moves</name>
      <value>50</value>
    </property>
    
    <property>
      <name>dfs.datanode.data.dir</name>
      <value>/data1/hadoop/hdfs/data,/data2/hadoop/hdfs/data,/data3/hadoop/hdfs/data,/data4/hadoop/hdfs/data,/data5/hadoop/hdfs/data,/data6/hadoop/hdfs/data,/data7/hadoop/hdfs/data,/data8/hadoop/hdfs/data,/data9/hadoop/hdfs/data,/data10/hadoop/hdfs/data,/data11/hadoop/hdfs/data,/data12/hadoop/hdfs/data,/data13/hadoop/hdfs/data,/data14/hadoop/hdfs/data,/data15/hadoop/hdfs/data,/data16/hadoop/hdfs/data,/data17/hadoop/hdfs/data,/data18/hadoop/hdfs/data,/data19/hadoop/hdfs/data,/data20/hadoop/hdfs/data,/data21/hadoop/hdfs/data,/data22/hadoop/hdfs/data,/data23/hadoop/hdfs/data</value>
    </property>
    
    <property>
      <name>dfs.datanode.data.dir.perm</name>
      <value>750</value>
    </property>
    
    <property>
      <name>dfs.datanode.du.reserved</name>
      <value>1073741824</value>
    </property>
    
    <property>
      <name>dfs.datanode.failed.volumes.tolerated</name>
      <value>0</value>
    </property>
    
    <property>
      <name>dfs.datanode.fsdataset.volume.choosing.policy</name>
      <value>org.apache.hadoop.hdfs.server.datanode.fsdataset.AvailableSpaceVolumeChoosingPolicy</value>
    </property>
    
    <property>
      <name>dfs.datanode.handler.count</name>
      <value>128</value>
    </property>
    
    <property>
      <name>dfs.datanode.http.address</name>
      <value>0.0.0.0:50075</value>
    </property>
    
    <property>
      <name>dfs.datanode.https.address</name>
      <value>0.0.0.0:50475</value>
    </property>
    
    <property>
      <name>dfs.datanode.ipc.address</name>
      <value>0.0.0.0:8010</value>
    </property>
    
    <property>
      <name>dfs.datanode.max.transfer.threads</name>
      <value>10240</value>
    </property>
    
    <property>
      <name>dfs.datanode.max.xcievers</name>
      <value>10240</value>
    </property>
    
    <property>
      <name>dfs.domain.socket.path</name>
      <value>/var/lib/hadoop-hdfs/dn_socket</value>
    </property>
    
    <property>
      <name>dfs.ha.automatic-failover.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.ha.balancer.request.standby</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.ha.fencing.methods</name>
      <value>shell(/bin/true)</value>
    </property>
    
    <property>
      <name>dfs.ha.log-roll.period</name>
      <value>120</value>
    </property>
    
    <property>
      <name>dfs.ha.namenodes.hdfsCluster</name>
      <value>nn1,nn2,nn3</value>
    </property>
    
    <property>
      <name>dfs.ha.tail-edits.number</name>
      <value>100000</value>
    </property>
    
    <property>
      <name>dfs.ha.tail-edits.period</name>
      <value>60</value>
    </property>
    
    <property>
      <name>dfs.heartbeat.interval</name>
      <value>10</value>
    </property>
    
    <property>
      <name>dfs.hosts.exclude</name>
      <value>/etc/hadoop/conf.empty/dfs.exclude</value>
    </property>
    
    <property>
      <name>dfs.http.policy</name>
      <value>HTTP_ONLY</value>
    </property>
    
    <property>
      <name>dfs.https.port</name>
      <value>50470</value>
    </property>
    
    <property>
      <name>dfs.journalnode.edits.dir</name>
      <value>/data/hadoop/hdfs/journalnode</value>
    </property>
    
    <property>
      <name>dfs.journalnode.http-address</name>
      <value>0.0.0.0:8480</value>
    </property>
    
    <property>
      <name>dfs.journalnode.https-address</name>
      <value>0.0.0.0:8481</value>
    </property>
    
    <property>
      <name>dfs.namenode.accesstime.precision</name>
      <value>0</value>
    </property>
    
    <property>
      <name>dfs.namenode.acls.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.namenode.audit.log.async</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.namenode.avoid.read.stale.datanode</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.namenode.avoid.write.stale.datanode</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.check.period</name>
      <value>300</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.dir</name>
      <value>/data/hadoop/hdfs/namesecondary</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.edits.dir</name>
      <value>${dfs.namenode.checkpoint.dir}</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.max-retries</name>
      <value>3</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.period</name>
      <value>5400</value>
    </property>
    
    <property>
      <name>dfs.namenode.checkpoint.txns</name>
      <value>20000000</value>
    </property>
    
    <property>
      <name>dfs.namenode.datanode.registration.ip-hostname-check</name>
      <value>false</value>
    </property>
    
    <property>
      <name>dfs.namenode.enable.retrycache</name>
      <value>false</value>
    </property>
    
    <property>
      <name>dfs.namenode.handler.count</name>
      <value>256</value>
    </property>
    
    <property>
      <name>dfs.namenode.http-address</name>
      <value>master03:50070</value>
    </property>
    
    <property>
      <name>dfs.namenode.http-address.hdfsCluster.nn1</name>
      <value>master03:50070</value>
    </property>
    
    <property>
      <name>dfs.namenode.http-address.hdfsCluster.nn2</name>
      <value>master04:50070</value>
    </property>
    
    <property>
      <name>dfs.namenode.http-address.hdfsCluster.nn3</name>
      <value>master05:50070</value>
    </property>
    
    <property>
      <name>dfs.namenode.https-address</name>
      <value>master03:50470</value>
    </property>
    
    <property>
      <name>dfs.namenode.lifeline.rpc-address.hdfsCluster.nn1</name>
      <value>master03:9040</value>
    </property>
    
    <property>
      <name>dfs.namenode.lifeline.rpc-address.hdfsCluster.nn2</name>
      <value>master04:9040</value>
    </property>
    
    <property>
      <name>dfs.namenode.lifeline.rpc-address.hdfsCluster.nn3</name>
      <value>master05:9040</value>
    </property>
    
    <property>
      <name>dfs.namenode.max.extra.edits.segments.retained</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>dfs.namenode.name.dir</name>
      <value>/data/hadoop/hdfs/namenode</value>
    </property>
    
    <property>
      <name>dfs.namenode.name.dir.restore</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.namenode.num.extra.edits.retained</name>
      <value>20000000</value>
    </property>
    
    <property>
      <name>dfs.namenode.quota.init-threads</name>
      <value>18</value>
    </property>
    
    <property>
      <name>dfs.namenode.replication.max-streams</name>
      <value>100</value>
    </property>
    
    <property>
      <name>dfs.namenode.replication.max-streams-hard-limit</name>
      <value>150</value>
    </property>
    
    <property>
      <name>dfs.namenode.replication.work.multiplier.per.iteration</name>
      <value>50</value>
    </property>
    
    <property>
      <name>dfs.namenode.rpc-address.hdfsCluster.nn1</name>
      <value>master03:8020</value>
    </property>
    
    <property>
      <name>dfs.namenode.rpc-address.hdfsCluster.nn2</name>
      <value>master04:8020</value>
    </property>
    
    <property>
      <name>dfs.namenode.rpc-address.hdfsCluster.nn3</name>
      <value>master05:8020</value>
    </property>
    
    <property>
      <name>dfs.namenode.safemode.threshold-pct</name>
      <value>1.0f</value>
    </property>
    
    <property>
      <name>dfs.namenode.secondary.http-address</name>
      <value>${HDFS.SECONDARY_NAMENODE}:50090</value>
    </property>
    
    <property>
      <name>dfs.namenode.servicerpc-address.hdfsCluster.nn1</name>
      <value>master03:8021</value>
    </property>
    
    <property>
      <name>dfs.namenode.servicerpc-address.hdfsCluster.nn2</name>
      <value>master04:8021</value>
    </property>
    
    <property>
      <name>dfs.namenode.servicerpc-address.hdfsCluster.nn3</name>
      <value>master05:8021</value>
    </property>
    
    <property>
      <name>dfs.namenode.shared.edits.dir</name>
      <value>qjournal://master01:8485;master02:8485;master03:8485/hdfsCluster</value>
    </property>
    
    <property>
      <name>dfs.namenode.stale.datanode.interval</name>
      <value>30000</value>
    </property>
    
    <property>
      <name>dfs.namenode.startup.delay.block.deletion.sec</name>
      <value>3600</value>
    </property>
    
    <property>
      <name>dfs.namenode.write.stale.datanode.ratio</name>
      <value>1.0f</value>
    </property>
    
    <property>
      <name>dfs.nameservices</name>
      <value>hdfsCluster</value>
    </property>
    
    <property>
      <name>dfs.permissions.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.permissions.superusergroup</name>
      <value>hdfs</value>
    </property>
    
    <property>
      <name>dfs.qjournal.write-txns.timeout.ms</name>
      <value>40000</value>
    </property>
    
    <property>
      <name>dfs.replication</name>
      <value>3</value>
    </property>
    
    <property>
      <name>dfs.replication.max</name>
      <value>50</value>
    </property>
    
    <property>
      <name>dfs.support.append</name>
      <value>true</value>
    </property>
    
    <property>
      <name>dfs.webhdfs.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>fs.AbstractFileSystem.alluxio.impl</name>
      <value>alluxio.hadoop.AlluxioFileSystem</value>
    </property>
    
    <property>
      <name>fs.alluxio-ft.impl</name>
      <value>alluxio.hadoop.FaultTolerantFileSystem</value>
    </property>
    
    <property>
      <name>fs.alluxio.impl</name>
      <value>alluxio.hadoop.FileSystem</value>
    </property>
    
    <property>
      <name>fs.getspaceused.classname</name>
      <value>org.apache.hadoop.fs.DFCachingGetSpaceUsed</value>
    </property>
    
    <property>
      <name>fs.permissions.umask-mode</name>
      <value>002</value>
    </property>
    
    <property>
      <name>ha.health-monitor.rpc-timeout.ms</name>
      <value>90000</value>
    </property>
    
    <property>
      <name>ha.zookeeper.parent-znode</name>
      <value>/hadoop-ha</value>
    </property>
    
    <property>
      <name>ha.zookeeper.quorum</name>
      <value>master02:2181,master03:2181,master04:2181,master05:2181,master01:2181</value>
    </property>
    
    <property>
      <name>ipc.server.listen.queue.size</name>
      <value>512</value>
    </property>
    
    <property>
      <name>ipc.server.read.threadpool.size</name>
      <value>4</value>
    </property>
    
    <property>
      <name>namenode.jmx.port</name>
      <value>8700</value>
    </property>
    
    <property>
      <name>tbds.namenode.format.enable</name>
      <value>true</value>
    </property>
    
  </configuration>
