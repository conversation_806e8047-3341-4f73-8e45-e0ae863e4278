# 计数比较规则 (Count Comparison Rule) 实现计划

## 背景 (Background)

当前系统中的 `RuleInfo` 类支持 SQL 查询规则 (SQL Query Rule)，现在需要扩展其功能以支持计数比较规则 (Count Comparison Rule)。计数比较规则属性已经添加到 `RuleInfo` 类中，但需要进一步修改系统以支持这种新的规则类型。

## 目标 (Objectives)

1. 使 `RuleInfo` 类能够同时支持 SQL 查询规则和计数比较规则
2. 修改 `TaskConsumer` 类以处理两种不同类型的规则
3. 实现规则类型的确定和验证逻辑
4. 实现计数比较规则的处理逻辑，同时保留现有 SQL 查询规则的处理逻辑

## 实现计划 (Implementation Plan)

### 1. 修改 `RuleInfo` 类 (Modify RuleInfo Class)

将原始 SQL 查询规则的必选属性设置为可空 (nullable)：

```kotlin
data class RuleInfo(
    val ruleId: Long,
    val ruleName: String,

    // SQL 查询规则属性 (SQL Query Rule Properties)
    val dataSource: String? = null,         // 改为可空
    val ruleSql: String? = null,            // 改为可空
    val countSql: String? = null,           // 改为可空
    val businessKeyList: List<String>? = null, // 改为可空
    val databaseName: String? = null,

    // 计数比较规则属性 (Count Comparison Rule Properties)
    val sourceSql: String? = null,
    val targetSql: String? = null,
    val sourceDatabaseName: String? = null,
    val sourceDataSource: String? = null,
    val targetDatabaseName: String? = null,
    val targetDataSource: String? = null,
    val comparisonType: ComparisonType? = null,
    val threshold: Double? = null,

    val relatedConfig: Map<String, Any?>? = null
)
```

### 2. 添加规则类型枚举 (Add Rule Type Enum)

创建一个枚举类来区分不同类型的规则：

```kotlin
enum class RuleType {
    SQL_QUERY,           // SQL 查询规则
    COUNT_COMPARISON     // 计数比较规则
}
```

### 3. 扩展 `RuleInfo` 类型判断函数 (Extend RuleInfo with Type Determination)

为 `RuleInfo` 添加扩展函数以确定规则类型：

```kotlin
fun RuleInfo.determineRuleType(): RuleType {
    return when {
        // 当存在计数比较规则必要属性时判定为计数比较规则
        sourceSql != null && targetSql != null && comparisonType != null && threshold != null -> RuleType.COUNT_COMPARISON
        // 否则判定为 SQL 查询规则
        else -> RuleType.SQL_QUERY
    }
}
```

### 4. 添加规则验证逻辑 (Add Rule Validation Logic)

创建规则验证函数，根据规则类型验证必要属性：

```kotlin
fun RuleInfo.validate(): Result<Boolean> {
    return when (determineRuleType()) {
        RuleType.SQL_QUERY -> validateSqlQueryRule()
        RuleType.COUNT_COMPARISON -> validateCountComparisonRule()
    }
}

private fun RuleInfo.validateSqlQueryRule(): Result<Boolean> {
    if (dataSource.isNullOrBlank() || 
        ruleSql.isNullOrBlank() || 
        countSql.isNullOrBlank() ||
        businessKeyList.isNullOrEmpty()) {
        return Result.failure(IllegalArgumentException("SQL Query Rule 缺少必要属性"))
    }
    return Result.success(true)
}

private fun RuleInfo.validateCountComparisonRule(): Result<Boolean> {
    if (sourceSql.isNullOrBlank() ||
        targetSql.isNullOrBlank() ||
        sourceDataSource.isNullOrBlank() ||
        targetDataSource.isNullOrBlank() ||
        comparisonType == null ||
        threshold == null) {
        return Result.failure(IllegalArgumentException("Count Comparison Rule 缺少必要属性"))
    }
    return Result.success(true)
}
```

### 5. 修改 `TaskConsumer.processRule` 方法 (Modify TaskConsumer.processRule Method)

修改 `processRule` 方法以处理不同类型的规则：

```kotlin
fun processRule(rule: RuleInfo, context: TaskMessage): Result<Int> {
    // 验证规则
    val validationResult = rule.validate()
    if (validationResult.isFailure) {
        return Result.failure(validationResult.exceptionOrNull() ?: 
               IllegalArgumentException("规则验证失败"))
    }

    // 根据规则类型调用不同的处理逻辑
    return when (rule.determineRuleType()) {
        RuleType.SQL_QUERY -> processSqlQueryRule(rule, context)
        RuleType.COUNT_COMPARISON -> processCountComparisonRule(rule, context)
    }
}
```

### 6. 实现计数比较规则处理逻辑 (Implement Count Comparison Rule Processing Logic)

创建新方法 `processCountComparisonRule` 以处理计数比较规则：

```kotlin
private fun processCountComparisonRule(rule: RuleInfo, context: TaskMessage): Result<Int> {
    // 1. 获取源和目标数据库连接
    val sourceConnection = createDatabaseConnection(rule, isSource = true)
    val targetConnection = createDatabaseConnection(rule, isTarget = true)
    
    // 2. 执行源和目标 SQL 查询
    val sourceCount = executeCountQuery(sourceConnection, rule.sourceSql!!)
    val targetCount = executeCountQuery(targetConnection, rule.targetSql!!)
    
    // 3. 根据比较类型和阈值进行比较
    val comparisonResult = compareResults(sourceCount, targetCount, rule.comparisonType!!, rule.threshold!!)
    
    // 4. 记录比较结果并根据结果决定是否需要创建问题
    if (!comparisonResult) {
        // 创建问题记录
        createIssue(rule, context, sourceCount, targetCount)
        return Result.success(1) // 表示找到一个问题
    }
    
    return Result.success(0) // 表示没有问题
}
```

### 7. 为计数比较规则添加辅助函数 (Add Helper Functions for Count Comparison Rule)

添加实现计数比较规则所需的辅助函数：

```kotlin
// 创建数据库连接的重载方法，支持源/目标数据库
private fun createDatabaseConnection(rule: RuleInfo, isSource: Boolean = false, isTarget: Boolean = false): DatabaseConnectionInfo {
    val dataSource = when {
        isSource -> rule.sourceDataSource!!
        isTarget -> rule.targetDataSource!!
        else -> rule.dataSource!!
    }
    
    val databaseName = when {
        isSource -> rule.sourceDatabaseName
        isTarget -> rule.targetDatabaseName
        else -> rule.databaseName
    }
    
    // 使用现有的连接创建逻辑
    // ...
}

// 执行计数查询
private fun executeCountQuery(connection: DatabaseConnectionInfo, sql: String): Long {
    // 使用连接执行 SQL 并返回计数结果
    // ...
}

// 比较源和目标结果
private fun compareResults(
    sourceCount: Long, 
    targetCount: Long, 
    comparisonType: ComparisonType, 
    threshold: Double
): Boolean {
    return when (comparisonType) {
        ComparisonType.ABSOLUTE_DIFFERENCE -> {
            val difference = Math.abs(targetCount - sourceCount)
            difference <= threshold
        }
        ComparisonType.TARGET_DIV_SOURCE_RATIO -> {
            if (sourceCount == 0L) return false
            val ratio = targetCount.toDouble() / sourceCount.toDouble()
            Math.abs(1 - ratio) <= threshold
        }
        ComparisonType.SOURCE_DIV_TARGET_RATIO -> {
            if (targetCount == 0L) return false
            val ratio = sourceCount.toDouble() / targetCount.toDouble()
            Math.abs(1 - ratio) <= threshold
        }
    }
}

// 创建问题记录
private fun createIssue(rule: RuleInfo, context: TaskMessage, sourceCount: Long, targetCount: Long): Long {
    // 实现创建问题记录的逻辑
    // ...
}
```

### 8. 重构现有的 `processRule` 方法 (Refactor Existing processRule Method)

将现有的 SQL 查询规则处理逻辑提取到新的 `processSqlQueryRule` 方法中：

```kotlin
private fun processSqlQueryRule(rule: RuleInfo, context: TaskMessage): Result<Int> {
    // 复用现有的 processRule 方法中处理 SQL 查询规则的逻辑
    // ...
}
```

## 测试计划 (Testing Plan)

1. 单元测试 (Unit Tests)
   - 测试 `RuleInfo.determineRuleType()` 函数
   - 测试 `RuleInfo.validate()` 函数
   - 测试 `compareResults()` 函数

2. 集成测试 (Integration Tests)
   - 测试 SQL 查询规则的完整流程
   - 测试计数比较规则的完整流程
   - 测试混合规则类型的场景

3. 端到端测试 (End-to-End Tests)
   - 通过 Kafka 发送包含不同类型规则的任务消息
   - 验证系统正确处理两种规则类型

## 风险和缓解措施 (Risks and Mitigation)

1. 向后兼容性 (Backward Compatibility)
   - 风险：现有规则可能不符合新的验证逻辑
   - 缓解：确保现有规则数据在迁移时进行验证，必要时进行数据修复

2. 性能影响 (Performance Impact)
   - 风险：计数比较规则需要执行两个查询，可能影响处理效率
   - 缓解：考虑使用并行执行源和目标查询

3. 错误处理 (Error Handling)
   - 风险：新规则类型引入新的错误场景
   - 缓解：完善错误处理和日志记录，确保问题可追踪

## 后续优化方向 (Future Optimizations)

1. 支持更多比较类型 (Support More Comparison Types)
2. 添加更丰富的规则配置选项 (Add More Rule Configuration Options)
3. 实现规则统计和分析功能 (Implement Rule Statistics and Analysis)
4. 优化大数据量场景下的性能 (Optimize Performance for Large Data Volumes)
