package com.datayes.executor

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType
import strikt.api.expectThat
import strikt.assertions.containsKeys
import strikt.assertions.hasSize
import javax.sql.DataSource

class JdbcQueryExecutorTest {

    private lateinit var dataSource: DataSource
    private lateinit var queryExecutor: JdbcQueryExecutor

    @BeforeEach
    fun setup() {
        // 创建内存数据库并初始化测试数据
        // Use DATABASE_TO_UPPER=FALSE to preserve case of identifiers
        dataSource = EmbeddedDatabaseBuilder()
            .setType(EmbeddedDatabaseType.H2)
            .setName("testdb;DATABASE_TO_UPPER=FALSE")
            .build()

        // Initialize database with schema and test data
        val schemaSQL = """
            DROP TABLE IF EXISTS table2;
            DROP TABLE IF EXISTS table1;

            CREATE TABLE table1 (
                id INT PRIMARY KEY,
                name VARCHAR(255)
            );

            CREATE TABLE table2 (
                id INT PRIMARY KEY,
                table1_id INT,
                description VARCHAR(255),
                FOREIGN KEY (table1_id) REFERENCES table1(id)
            );
        """.trimIndent()

        val dataSQL = """
            INSERT INTO table1 (id, name) VALUES (1, 'Item 1');
            INSERT INTO table1 (id, name) VALUES (2, 'Item 2');

            INSERT INTO table2 (id, table1_id, description) VALUES (1, 1, 'Description for Item 1');
            INSERT INTO table2 (id, table1_id, description) VALUES (2, 2, 'Description for Item 2');
        """.trimIndent()

        dataSource.connection.use { conn ->
            conn.createStatement().use { stmt ->
                stmt.executeUpdate(schemaSQL)
                stmt.executeUpdate(dataSQL)
            }
        }

        queryExecutor = JdbcQueryExecutor(dataSource)
    }

    @AfterEach
    fun cleanup() {
        // 关闭数据库连接
        if (dataSource is AutoCloseable) {
            (dataSource as AutoCloseable).close()
        }
    }

    @Test
    fun `should handle columns with same name in join query`() {
        // 执行包含相同列名的连接查询
        val sql = """
            SELECT t1.id, t2.id, t1.name, t2.description 
            FROM table1 t1 
            JOIN table2 t2 ON t1.id = t2.table1_id
        """.trimIndent()

        // 执行查询
        val result = queryExecutor.queryPage(sql)

        // 验证结果
        expectThat(result).hasSize(2)

        // 验证第一行数据
        val firstRow = result[0]

        expectThat(firstRow).containsKeys(
            "table1.id", "table1.id", "name", "description"
        )
    }

    @Test
    fun `should handle single table query without table name prefix`() {
        // 执行单表查询
        val sql = "SELECT id, name FROM table1"

        // 执行查询
        val result = queryExecutor.queryPage(sql)

        // 验证结果
        expectThat(result).hasSize(2)

        // 验证第一行数据
        val firstRow = result[0]

        expectThat(firstRow).containsKeys(
            "id", "name"
        )
    }
}
