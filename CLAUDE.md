# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Data Quality Processing/Rule Execution System** (`dgp-issue-executor`) built as a Spring Boot microservice in Kotlin. The application executes data quality rules, processes results, and manages issues in a distributed environment with Kafka message processing and multi-database connectivity (MySQL, PostgreSQL, Oracle, Hive).

**Main Entry Point**: `src/main/kotlin/com/datayes/App.kt`

## Common Commands

### Build & Run
```bash
# Build the project
mvn clean compile

# Run tests
mvn test

# Run a single test class
mvn test -Dtest=TaskMessageTest

# Run a single test method
mvn test -Dtest=TaskMessageTest#testTaskMessageSerialization

# Package application
mvn clean package

# Run the application
mvn spring-boot:run
```

### Development
```bash
# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Debug mode
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
```

## Architecture Overview

### Core Components

**Domain Layer** (`domain/`):
- `Issue.kt` - Core issue entities with lifecycle management (NOT_STARTED → WAITING → RUNNING → COMPLETED/ERROR/CRASHED)
- `TaskMessage.kt` - Kafka message structures for rule execution
- `RuleExtensions.kt` - Rule validation and type determination logic
- Entities for metadata, execution logs, and rule results

**Executor Layer** (`executor/`):
- `DatabaseExecutor.kt` - Multi-database query execution engine with pagination
- `ResultBatchProcessor.kt` - Batch processing for rule execution results
- Supports MySQL, PostgreSQL, Oracle, and Hive databases

**Spring Layer** (`spring/`):
- Controllers for heartbeat and task management APIs
- Repository layer using Spring Data JDBC
- `TaskConsumer.kt` - Main Kafka consumer for processing rule execution tasks
- Host-based distributed coordination and stale process cleanup

**Hive Integration** (`hive/`):
- `HiveQueryExecutor.kt` - Specialized big data processing capabilities

### Rule Types

1. **SQL_QUERY Rules**: Execute paginated SQL queries with result storage and business key extraction
2. **COUNT_COMPARISON Rules**: Compare count results between databases using configurable thresholds and comparison types (ABSOLUTE_DIFFERENCE, ratio-based comparisons)

### Message Processing Flow
1. Kafka consumer receives `TaskMessage` from `data-quality-tasks` topic
2. Rule validation and type determination
3. Distributed issue acquisition with optimistic locking
4. Database connection establishment and query execution
5. Result processing and storage with batch operations
6. Status updates and dependent post-task execution

## Technology Stack

- **Spring Boot 3.4.4** with Kotlin 1.9.25 and Java 17
- **Spring Data JDBC** for data access
- **Apache Kafka** for distributed message processing
- **Multi-database support**: MySQL (primary), PostgreSQL, Oracle, Apache Hive 2.2.0
- **Testing**: JUnit 5, Strikt assertions, ArchUnit for architectural testing
- **JSON Processing**: Jackson with Kotlin module

## Testing Guidelines

### Repository Tests
Use `@DataJdbcTest` and `@AutoConfigureTestDatabase` annotations for repository tests:

```kotlin
@DataJdbcTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class SomeRepositoryTest {
    // Test implementation
}
```

This ensures:
- Focused data access layer testing
- Faster test execution with minimal context loading
- Automatic transaction rollback between tests
- Real database usage instead of in-memory alternatives

### Test Structure
- **Unit Tests**: Domain logic and utilities
- **Integration Tests**: Database and Kafka connectivity
- **Architectural Tests**: Package dependency validation using ArchUnit
- **Manual Tests**: Kafka consumer testing for development scenarios

## Configuration

### Key Application Properties
- Database: MySQL primary connection to `***********:3306/dgp`
- Kafka: Bootstrap servers at `***********:9092` with consumer group `executor-group-task`
- Concurrency: 10 listeners with max 500 poll records
- Jackson: ISO date formatting (not timestamps)

### Deployment
- **Service Name**: `data-quality-executor-service`
- **Port**: 9501
- **Kubernetes**: Helm charts available in `/charts/dgp-issue-executor/`
- **Multi-replica support** with distributed coordination via host identification

## Important Patterns

### Distributed Processing
- Host-based task distribution using `HostInfo`
- Optimistic locking with version-based entity updates
- Automatic cleanup of stale RUNNING processes
- Distributed coordination to prevent duplicate processing

### Error Handling
- Comprehensive status tracking through issue lifecycle
- Cancel context support for long-running operations
- Retry mechanisms and graceful degradation
- Structured logging with execution tracking

### Performance Considerations
- Configurable pagination for large result sets
- Batch processing for rule result storage
- Connection pooling with HikariCP
- In-memory logging utilities for development debugging