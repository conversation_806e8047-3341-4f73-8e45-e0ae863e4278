package com.datayes.spring

import com.datayes.domain.IssueEntity
import com.datayes.domain.IssueStage
import org.springframework.data.repository.ListCrudRepository
import java.time.LocalDateTime

interface IssueRepository : ListCrudRepository<IssueEntity, Long> {

    fun findByRuleId(ruleId: Long): List<IssueEntity>

    fun findByRuleIdAndTaskId(ruleId: Long, taskId: Long): List<IssueEntity>

    fun findByRuleIdAndTaskIdAndCurrentStage(ruleId: Long, taskId: Long, currentStage: IssueStage): IssueEntity?

    fun findByCurrentStageAndUpdatedAtBefore(currentStage: IssueStage, cutoffTime: LocalDateTime): List<IssueEntity>
}
