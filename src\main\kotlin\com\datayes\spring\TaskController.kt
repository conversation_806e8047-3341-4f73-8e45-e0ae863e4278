package com.datayes.spring

import com.datayes.domain.RuleInfo
import com.datayes.domain.TaskDetail
import com.datayes.domain.TaskMessage
import com.datayes.spring.kafka.TaskConsumer
import com.datayes.spring.StandardResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.kafka.support.Acknowledgment
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/tasks")
@Tag(name = "任务控制器", description = "提供数据质量任务相关操作的API")
class TaskController(private val taskConsumer: TaskConsumer) {

    @PostMapping("/execute")
    @Operation(
        summary = "执行数据质量任务", 
        description = "手动触发执行数据质量任务，与Kafka消息触发效果相同"
    )
    fun executeTask(@RequestBody taskRequest: TaskRequest): StandardResponse<String> {
        val taskMessage = TaskMessage(
            taskId = taskRequest.taskId,
            taskName = taskRequest.taskName,
            rules = taskRequest.rules,
            postTasks = taskRequest.postTasks ?: emptyList(),
            runId = taskRequest.runId,
            operator = taskRequest.operator
        )
        
        // 为手动执行创建一个模拟的 acknowledgment (Create a mock acknowledgment for manual execution)
        val mockAcknowledgment = Acknowledgment {
            // 手动执行时不需要真正的消息确认 (No real message acknowledgment needed for manual execution)
        }
        
        taskConsumer.onMessage(taskMessage, mockAcknowledgment)
        
        return StandardResponse.success("任务已提交执行 (Task submitted for execution)")
    }
}

/**
 * 任务请求数据类 (Task Request Data Class)
 */
data class TaskRequest(
    val taskId: Long,
    val taskName: String?,
    val rules: List<RuleInfo>,
    val postTasks: List<TaskDetail>? = null,
    val runId: String,
    val operator: String
)
