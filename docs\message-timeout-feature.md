# 消息处理硬超时功能 (Message Processing Hard Timeout Feature)

## 概述 (Overview)

此功能为每个 Kafka 消息处理设置硬超时 (hard timeout)，如果消息处理时间超过配置的超时时间，系统将强制结束处理并将消息标记为已处理，同时记录错误结果。

## 核心特性 (Core Features)

### 1. 可配置超时时间 (Configurable Timeout)
- 通过配置属性 `app.message.processing.timeout.minutes` 设置超时时间（单位：分钟）
- 默认值：30 分钟
- 可以通过环境变量或配置文件动态调整

### 2. 优雅超时处理 (Graceful Timeout Handling)
- 使用 `CompletableFuture` 和 `ScheduledExecutorService` 实现非阻塞超时控制
- 超时发生时：
  - 记录错误日志到执行日志表 (RuleExecutionLogEntity)
  - 更新问题状态为 ERROR (IssueStage.ERROR)
  - 更新运行历史状态为 TIMEOUT (RunStatus.TIMEOUT)
  - 消息仍然被确认，避免重复处理

### 3. 资源清理 (Resource Cleanup)
- 专用线程池 (`messageTimeoutExecutor`) 处理超时任务
- 优雅关闭时取消所有活动的超时任务
- 防止资源泄漏

## 配置 (Configuration)

### application.properties
```properties
# 消息处理硬超时配置 (单位：分钟)
app.message.processing.timeout.minutes=30
```

### 环境变量
```bash
export APP_MESSAGE_PROCESSING_TIMEOUT_MINUTES=45
```

## 技术实现 (Technical Implementation)

### 架构概览 (Architecture Overview)

```
TaskConsumer.onMessage()
    ↓
创建 CompletableFuture (Create CompletableFuture)
    ↓
设置超时任务 (Setup timeout task)
    ↓
并行执行 (Parallel execution):
    ├── 消息处理逻辑 (Message processing logic)
    └── 超时监控任务 (Timeout monitoring task)
    ↓
处理完成或超时 (Complete or timeout)
    ↓
清理资源 + 确认消息 (Cleanup + acknowledge)
```

### 关键组件 (Key Components)

1. **TaskProcessingContext**: 跟踪每个消息处理的上下文，包括超时任务引用
2. **messageTimeoutExecutor**: 专用调度器，管理超时任务
3. **markRuleAsTimedOut()**: 处理超时状态更新的核心方法

### 超时处理流程 (Timeout Handling Flow)

1. 消息接收时创建 `CompletableFuture` 包装处理逻辑
2. 同时创建定时任务，在超时时间后检查处理状态
3. 如果到时间处理还未完成：
   - 记录超时错误日志
   - 更新数据库状态
   - 强制完成 Future
4. 无论成功还是超时，都确认消息

## 数据库变更 (Database Changes)

### RunStatus 枚举新增 (New RunStatus Enum)
```kotlin
enum class RunStatus {
    STARTED,
    COMPLETED,
    FAILED,
    TIMEOUT  // 新增超时状态
}
```

### Repository 新增方法 (New Repository Methods)
```kotlin
// IssueRepository
fun findByRuleIdAndTaskIdAndCurrentStage(ruleId: Long, taskId: Long, currentStage: IssueStage): IssueEntity?

// IssueRunHistoryRepository  
fun findByIssueIdAndRunId(issueId: Long, runId: String): IssueRunHistoryEntity?
```

## 监控和日志 (Monitoring and Logging)

### 关键日志事件 (Key Log Events)
- `Message processing timed out`: 超时发生时的错误日志
- `Marked rule as timed out`: 成功更新超时状态
- `Message acknowledged`: 消息确认（超时情况下也会发生）

### 监控指标 (Monitoring Metrics)
- 执行日志表中 TIMEOUT 状态的记录数
- 问题表中因超时导致的 ERROR 状态数量
- 平均消息处理时间

## 测试 (Testing)

### 单元测试
- `TaskConsumerTimeoutTest`: 验证超时功能的正确性
- 使用1分钟超时配置进行快速测试
- 验证状态更新和消息确认

### 性能考虑 (Performance Considerations)
- 超时检查开销极小（单次调度任务）
- 不影响正常消息处理性能
- 专用线程池避免阻塞主要处理流程

## 使用建议 (Usage Recommendations)

1. **超时时间设置**: 根据业务复杂度和数据量合理设置，建议：
   - 简单查询：5-10分钟
   - 复杂分析：15-30分钟
   - 大数据处理：30-60分钟

2. **监控**: 定期检查超时记录，分析性能瓶颈

3. **调优**: 根据实际运行情况调整超时时间和线程池大小

## 故障排除 (Troubleshooting)

### 常见问题 (Common Issues)

1. **超时时间过短**: 导致正常任务被错误标记为超时
   - 解决方案：增加超时时间或优化查询性能

2. **资源泄漏**: 大量超时任务堆积
   - 解决方案：检查 messageTimeoutExecutor 线程池配置

3. **数据库锁定**: 超时更新时发生数据库争用
   - 解决方案：优化数据库索引，使用适当的事务隔离级别 