package com.datayes.executor

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.slf4j.LoggerFactory

class DatabaseExecutorHiveIntegrationTest {
    
    private val log = LoggerFactory.getLogger(DatabaseExecutorHiveIntegrationTest::class.java)

    @Test
    fun `test hive connection and query execution`() {
        // Given (准备测试数据)
        val dbType = "hive"
        val url = "*****************************,10.9.112.26:2181,10.9.112.32:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        val username = "ms_urp"
        val password = "mslife@95596"
        val ruleName = "test-hive-connection"
        
        // When (执行测试)
        val dataSource = createDataSource(dbType, url, username, password, ruleName)
        val queryExecutor = JdbcQueryExecutor(dataSource)
        
        // Then (验证结果)
        assertDoesNotThrow {
            val sql = "SELECT * FROM urp_dws.view_contidt_000001 LIMIT 1"
            val results = queryExecutor.queryPage(sql)
            
            // 验证查询结果
            assertNotNull(results)
            assertTrue(results.isNotEmpty(), "Query should return at least one row")
            
            // 打印结果以供调试
            log.info("Query results: $results")
            
            // 验证结果中包含数据
            val firstRow = results.first()
            assertFalse(firstRow.isEmpty(), "First row should contain data")
        }
    }
} 