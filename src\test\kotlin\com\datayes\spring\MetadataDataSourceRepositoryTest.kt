package com.datayes.spring

import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.springframework.boot.test.autoconfigure.data.jdbc.DataJdbcTest
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import strikt.api.expectCatching
import strikt.assertions.isSuccess
import javax.inject.Inject

@DataJdbcTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class MetadataDataSourceRepositoryTest {

    private val logger = LoggerFactory.getLogger(MetadataDataSourceRepositoryTest::class.java)

    @Inject
    lateinit var metadataDataSourceRepository: MetadataDataSourceRepository

    @Test
    fun `should find all data sources without exception`() {
        // 执行查询操作
        expectCatching {
            val dataSources = metadataDataSourceRepository.findAll()
            
            // 打印查询结果
            logger.info("找到 {} 个数据源", dataSources.count())
            dataSources.forEach { dataSource ->
                logger.info("数据源: ID={}, 名称={}, 类型={}, 状态={}",
                    dataSource.id,
                    dataSource.sourceName,
                    dataSource.dbType,
                    if (dataSource.activeFlag) "有效" else "无效"
                )
            }
        }.isSuccess()
    }
} 