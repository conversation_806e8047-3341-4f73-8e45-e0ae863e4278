package com.datayes.hive

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HiveQueryExecutorTest {

    @Test
    fun testConnectUseSingleNode_27() {
        // given
        val url = "**************************************"
        val username = "ms_urp"
        val password = "mslife@95596"
        val sql = "SELECT * FROM view_contidt_000001 LIMIT 1"

        // when
        val result = HiveQueryExecutor.executeHiveQuery(url, username, password, sql)

        // then
        // view_contidt_000001.grpappno: null, view_contidt_000001.grppolicyno: null, view_contidt_000001.appno: 11100511114765, view_contidt_000001.policyno: 86110020240210007905, view_contidt_000001.gpflag: 1, view_contidt_000001.statisticalsdate: 2024-11-12, view_contidt_000001.statisticalstime: 09:58:29, view_contidt_000001.statisticaledate: 2024-11-12, view_contidt_000001.statisticaletime: 09:58:29, view_contidt_000001.managecom: 86110007, view_contidt_000001.contvalue: 1000000.00, view_contidt_000001.continterval: 0, view_contidt_000001.effdate: , view_contidt_000001.paymode: , view_contidt_000001.iscomplete: , view_contidt_000001.curpaidtodate: , view_contidt_000001.currency: CNY, view_contidt_000001.edorno: , view_contidt_000001.agtcode: , view_contidt_000001.salesmanno: , view_contidt_000001.specimanno: , view_contidt_000001.staffno: , view_contidt_000001.nbsalesmanno: , view_contidt_000001.nbsalesmanbranch: , view_contidt_000001.nbagtbranch: , view_contidt_000001.companycode: 000052, view_contidt_000001.statisticalcaldate: 2024-11-12, view_contidt_000001.statisticalcaltime: 09:58:29, view_contidt_000001.systemcode: LIS, view_contidt_000001.pushdate: 2024-11-12, view_contidt_000001.pushtime: 09:58:29, view_contidt_000001.isdeleted: , view_contidt_000001.filingdate: 2024-11-11, view_contidt_000001.filingtime: 23:59:59
        assertThat(result).isNotNull()
        assertThat(result.size).isEqualTo(1)

        // appno
        assertThat(result[0]["view_contidt_000001.appno"]).isEqualTo("11100511114765")
    }

    @Test
    fun testConnectUseSingleNode_28() {
        // given
        val url = "**************************************"
        val username = "ms_urp"
        val password = "mslife@95596"
        val sql = "SELECT * FROM view_contidt_000001 LIMIT 1"

        // when
        val result = HiveQueryExecutor.executeHiveQuery(url, username, password, sql)

        // then
        assertThat(result).isNotNull()
        assertThat(result.size).isEqualTo(1)

        // appno
        assertThat(result[0]["view_contidt_000001.appno"]).isEqualTo("11100511114765")
    }

    @Test
    fun testConnectUseZkHa() {
        // given
        val url =
            "*****************************,10.9.112.26:2181,10.9.112.32:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        val username = "ms_urp"
        val password = "mslife@95596"
        val sql = "SELECT * FROM urp_dws.view_contidt_000001 LIMIT 1"

        // when
        val result = HiveQueryExecutor.executeHiveQuery(url, username, password, sql)

        // then
        assertThat(result).isNotNull()
        assertThat(result.size).isEqualTo(1)

        // appno
        assertThat(result[0]["view_contidt_000001.appno"]).isEqualTo("11100511114765")
    }

    @Test
    fun testSelectLimit100() {
        // given
        val url = "**************************************"
        val username = "ms_urp"
        val password = "mslife@95596"
        val sql = "SELECT * FROM view_contidt_000001 LIMIT 100"

        // when
        val result = HiveQueryExecutor.executeHiveQuery(url, username, password, sql)

        // then
        assertThat(result).isNotNull()
        // 检查返回结果数量不超过100
        assertThat(result.size).isLessThanOrEqualTo(100)
        // 可选：检查部分字段存在
        if (result.isNotEmpty()) {
            assertThat(result[0]).containsKey("view_contidt_000001.appno")
        }
    }
}
