package com.datayes.executor

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class EnsureHiveZooKeeperParamsTest {

    @Test
    fun `should throw exception for blank URL`() {
        assertThrows<IllegalArgumentException> {
            ensureHiveZooKeeperParams("")
        }
        assertThrows<IllegalArgumentException> {
            ensureHiveZooKeeperParams("   ")
        }
    }

    @Test
    fun `should return unchanged URL when both parameters exist`() {
        val urlWithBothParams = "***************************************************************************************"
        assertEquals(urlWithBothParams, ensureHiveZooKeeperParams(urlWithBothParams))
    }

    @Test
    fun `should add missing serviceDiscoveryMode parameter`() {
        val urlWithNamespaceOnly = "********************************************************"
        val expected = "********************************************************;serviceDiscoveryMode=zooKeeper"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithNamespaceOnly))
    }

    @Test
    fun `should add missing zooKeeperNamespace parameter`() {
        val urlWithDiscoveryOnly = "********************************************************"
        val expected = "***************************************************************************************"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithDiscoveryOnly))
    }

    @Test
    fun `should add both parameters to URL with existing parameters using semicolon`() {
        val urlWithOtherParams = "******************************************"
        val expected = "******************************************;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithOtherParams))
    }

    @Test
    fun `should add both parameters to URL with existing parameters using question mark`() {
        val urlWithQueryParams = "******************************************"
        val expected = "******************************************&serviceDiscoveryMode=zooKeeper&zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithQueryParams))
    }

    @Test
    fun `should add parameters to URL ending with database path`() {
        val urlWithDbPath = "***************************"
        val expected = "***************************;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithDbPath))
    }

    @Test
    fun `should add parameters to URL ending with slash`() {
        val urlWithSlash = "**********************/"
        val expected = "**********************/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithSlash))
    }

    @Test
    fun `should add parameters to simple URL without path`() {
        val simpleUrl = "**********************"
        val expected = "**********************;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(simpleUrl))
    }

    @Test
    fun `should handle multiple hosts in ZooKeeper cluster`() {
        val multiHostUrl = "***********************,host2:2181,host3:2181/db"
        val expected = "***********************,host2:2181,host3:2181/db;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(multiHostUrl))
    }

    @Test
    fun `should handle URL with mixed case parameters`() {
        val urlWithMixedCase = "**********************/db;SERVICEDISCOVERYMODE=zookeeper;zookeepernamespace=hiveserver2"
        // Function should still add parameters as it does case-sensitive matching
        val expected = "**********************/db;SERVICEDISCOVERYMODE=zookeeper;zookeepernamespace=hiveserver2;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2"
        assertEquals(expected, ensureHiveZooKeeperParams(urlWithMixedCase))
    }
} 