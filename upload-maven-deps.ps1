# mvnd clean install "-Dmaven.repo.local=maven_localRepository" -DskipTests

# (上传)(upload) Maven 本地库(local repository)到 Nexus 仓库
Param(
    [switch]$y = $false,
    [int]$n = 0
)
$ErrorActionPreference = "Stop"
$repoPath = Join-Path $PSScriptRoot "maven_localRepository"
$baseUrl = "https://nexus.minshenglife.com/repository/develop2dep"
$username = "urp"
$password = "urp"
$securePwd = ConvertTo-SecureString $password -AsPlainText -Force
$cred = New-Object System.Management.Automation.PSCredential($username, $securePwd)

# 获取所有文件，但排除 _remote.repositories 文件
$cmdCount = 0

# 首先收集所有文件
# $allFiles = Get-ChildItem -Path $repoPath -Recurse -File | Where-Object { $_.Name -ne "_remote.repositories" }
$allFiles = Get-ChildItem -Path $repoPath -Recurse -File | Where-Object { $_.Name -ne "_remote.repositories" -and $_.FullName -like "*h2*" }

# 预处理文件，只保留符合条件的文件
$validFiles = @()
foreach ($file in $allFiles) {
    $relativePath = $file.DirectoryName.Substring($repoPath.Length + 1)
    $parts = $relativePath.Split('\')
    
    if ($parts.Length -ge 3 -and $file.Name -ne "_remote.repositories") {
        $validFiles += $file
    }
}

# 如果指定了 -n 参数，只处理前 N 个文件
if ($n -gt 0) {
    $validFiles = $validFiles | Select-Object -First $n
}

# 如果没有有效文件，显示消息并退出
if ($validFiles.Count -eq 0) {
    Write-Host "没有找到符合条件的文件(No valid files found)"
    return
}

Write-Host "将处理 $($validFiles.Count) 个文件(Processing files)"

# 创建一个线程安全的计数器
$syncHash = [hashtable]::Synchronized(@{})
$syncHash.cmdCount = 0

# 创建一个锁对象用于同步
$lockObj = [System.Object]::new()

# 使用并行处理，并发度设置为 20
$validFiles | ForEach-Object -ThrottleLimit 20 -Parallel {
    $file = $_
    
    # 使用使用传入的变量
    $repoPath = $using:repoPath
    $baseUrl = $using:baseUrl
    $username = $using:username
    $password = $using:password
    $y = $using:y
    $syncHash = $using:syncHash
    
    # 使用 Substring(repoPath.Length +1) 来去掉路径分隔符，得到正确的相对路径
    $relativePath = $file.DirectoryName.Substring($repoPath.Length + 1)
    $parts = $relativePath.Split('\')
    # write-host $relativePath
    # write-host $parts
    # write-host $parts.Length
    # 我们已经预先过滤了文件，所以这里不需要再次检查
    $groupId = ($parts[0..($parts.Length -3)] -join '.')
    $artifactId = $parts[$parts.Length -2]
    $version = $parts[$parts.Length -1]
    $groupIdPath = $groupId -replace '\.', '/'
    $uploadUrl = "$baseUrl/$groupIdPath/$artifactId/$version/$($file.Name)"
    # Write-Host "正在(Uploading) 上传 $($file.Name) 到 $uploadUrl"

    # $env:https_proxy = $null; curl -v -u urp:urp -X PUT "https://nexus.minshenglife.com/repository/develop2dep/org/yaml/snakeyaml/2.3/snakeyaml-2.3.jar.1" -H "Content-Type: application/octet-stream" --data-binary "@D:\projects\dgp-issue-executor\maven_localRepository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar"
    $curlCmd = '$env:https_proxy = $null; curl -s -u ' + $username + ':' + $password + ' -X PUT "' + $uploadUrl + '" -H "Content-Type: application/octet-stream" --data-binary "@' + $file.FullName + '"'
    $cmdCount++
        
    # 在并行处理中，我们已经在外部限制了文件数量
    # 使用锁实现线程安全的计数器递增
    $lockObj = $using:lockObj
    [int]$currentCount = 0
        
    # 锁定代码块以确保原子操作
    [System.Threading.Monitor]::Enter($lockObj)
    try {
        $syncHash.cmdCount++
        $currentCount = $syncHash.cmdCount
    }
    finally {
        [System.Threading.Monitor]::Exit($lockObj)
    }
        
    if ($y) {
        Write-Host "执行命令(Executing command): ${currentCount} - $curlCmd"
        Invoke-Expression $curlCmd
    } else {
        Write-Output "${currentCount}: $curlCmd"
    }
    # 我们已经预先过滤了文件，所以不需要跳过逻辑
}