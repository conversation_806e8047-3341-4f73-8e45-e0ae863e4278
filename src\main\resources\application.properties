spring.application.name                                   = data-quality-executor-service

server.port                                               = 9501

spring.datasource.driver-class-name                       = com.mysql.cj.jdbc.Driver
spring.datasource.url                                     = *******************************************************************************************************************
spring.datasource.username                                = dbausr
spring.datasource.password                                = Qweasd23!
spring.datasource.hikari.maximum-pool-size                = 100

#spring.kafka.bootstrap-servers                                    = 10.24.90.177:9092,10.24.90.178:9092,10.24.90.179:9092
spring.kafka.bootstrap-servers                            = 10.9.135.31:9092
spring.kafka.consumer.auto-offset-reset                   = latest

spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS    = false

spring.kafka.listener.auto-startup                        = false

spring.jackson.deserialization.fail-on-unknown-properties = false

spring.kafka.consumer.properties.max.poll.interval.ms     = 3600000

spring.kafka.listener.concurrency                         = 10
spring.kafka.consumer.max-poll-records                    = 500

app.message.processing.timeout.minutes                    = 30
