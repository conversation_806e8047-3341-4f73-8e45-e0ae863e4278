-- DDL for the rule_execution_log table

CREATE TABLE rule_execution_log
(
    id        BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_id   BIGINT       NOT NULL,
    task_id   BIGINT       NOT NULL,
    run_id    VARCHAR(255) NOT NULL,
    level     VARCHAR(50)  NOT NULL,
    message   TEXT,
    host      VA<PERSON>HAR(255) NOT NULL,
    timestamp TIMESTAMP    NOT NULL,
    INDEX     idx_rule_id (rule_id),
    INDEX     idx_task_id (task_id),
    INDEX     idx_run_id (run_id),
    INDEX idx_timestamp (timestamp)
);
