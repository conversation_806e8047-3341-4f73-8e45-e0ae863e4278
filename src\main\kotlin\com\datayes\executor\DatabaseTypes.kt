package com.datayes.executor

import javax.sql.DataSource

/**
 * 数据库类型枚举 (Database Type Enum)
 */
enum class DatabaseType {
    MYSQL, ORACLE, POSTGRESQL, HIVE, TDSQL, TBASE;

    companion object {
        fun fromString(type: String): DatabaseType = when (type.lowercase()) {
            "mysql" -> MYSQL
            "oracle" -> ORACLE
            "postgresql" -> POSTGRESQL
            "hive" -> HIVE
            "tdsql" -> MYSQL
            "tbase" -> POSTGRESQL
            else -> throw IllegalArgumentException("Unsupported dbType: $type")
        }
    }
}

/**
 * 执行上下文数据类 (Execution Context Data Class)
 */
data class ExecutionContext(
    val dbType: DatabaseType,
    val dataSource: DataSource,
)

/**
 * 执行结果数据类 (Execution Result Data Class)
 */
data class CountResult(
    val count: Int,
    val executionTimeMs: Long,
)
