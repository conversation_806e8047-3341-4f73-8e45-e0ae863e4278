package com.datayes.kafka

import com.datayes.spring.JacksonConfig
import com.datayes.spring.kafka.createTaskMessageDeserializer
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TaskMessageDeserializerTest {

    @Test
    fun shouldDeserializeTaskMessageCorrectly() {
        val taskMessageDeserializer = createTaskMessageDeserializer(JacksonConfig().objectMapper())

        val message = """
            {
              "postTasks": [
                {
                  "rules": [
                    {
                      "ruleId": 36,
                      "ruleName": "测试-数据重复检查1",
                      "dataSource": 5,
                      "ruleSql": "SELECT StartDate,AgencyCode,AgtCode FROM (SELECT a.StartDate,a.AgencyCode,a.AgtCode FROM full_laagtcode1 as a WHERE (1=1)  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)) as temp GROUP BY StartDate,AgencyCode HAVING (count(1)>1)",
                      "countSql": "SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (1=1)",
                      "businessKeyList": [],
                      "databaseName": "usp_east",
                      "relatedConfig": null
                    },
                    {
                      "ruleId": 35,
                      "ruleName": "测试-数据为空校验1",
                      "dataSource": 5,
                      "ruleSql": "SELECT a.AgencyCode,a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))  AND ((a.AgencyCode is null or a.AgencyCode ='') AND (a.AgencyCertNo is null or a.AgencyCertNo =''))  AND (a.StartDate >= {yyyy-mm-dd}-2 AND a.StartDate <= {yyyy-mm-dd}-1)",
                      "countSql": "SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (AgtAddress in ('北京','上海','南京'))",
                      "businessKeyList": [],
                      "databaseName": "usp_east",
                      "relatedConfig": null
                    }
                  ],
                  "taskId": 26,
                  "taskName": "测试-后置任务1"
                }
              ],
              "rules": [
                {
                  "ruleId": 37,
                  "ruleName": "测试-前置任务1",
                  "dataSource": 5,
                  "ruleSql": "SELECT a.AgencyCertNo,a.AgtCode FROM full_laagtcode1 as a WHERE (1=1)  AND (((a.AgencyCertNo is null or a.AgencyCertNo ='')))",
                  "countSql": "SELECT COUNT(1) FROM full_laagtcode1 as a WHERE (1=1)",
                  "businessKeyList": [],
                  "databaseName": "usp_east",
                  "relatedConfig": null
                }
              ],
              "taskId": 25,
              "taskName": null,
              "runId": "3da5a1b4-a777-4e69-8890-8c0c2d3083ad",
              "operator": "admin"
            }
        """.trimIndent()

        val got = taskMessageDeserializer.deserialize("what-ever-topic", message.toByteArray(Charsets.UTF_8))

        assertThat(got).isNotNull
        assertThat(got?.taskId).isEqualTo(25)
        assertThat(got?.taskName).isNull()
        assertThat(got?.runId).isEqualTo("3da5a1b4-a777-4e69-8890-8c0c2d3083ad")
        assertThat(got?.operator).isEqualTo("admin")

        assertThat(got?.rules).hasSize(1)
        assertThat(got?.rules?.get(0)?.ruleId).isEqualTo(37)
        assertThat(got?.rules?.get(0)?.ruleName).isEqualTo("测试-前置任务1")

        assertThat(got?.postTasks).hasSize(1)
        assertThat(got?.postTasks?.get(0)?.taskId).isEqualTo(26)
        assertThat(got?.postTasks?.get(0)?.taskName).isEqualTo("测试-后置任务1")
        assertThat(got?.postTasks?.get(0)?.rules).hasSize(2)
        assertThat(got?.postTasks?.get(0)?.rules?.map { it.ruleId }).containsExactly(36, 35)
    }
}