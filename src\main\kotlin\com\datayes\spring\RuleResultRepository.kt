package com.datayes.spring

import com.datayes.domain.RuleResultEntity
import org.springframework.data.repository.ListCrudRepository
import org.springframework.stereotype.Repository

@Repository
interface RuleResultRepository : ListCrudRepository<RuleResultEntity, Long> {

    // find by rule id and rowDataMd5
    // fun findByRuleIdAndRowDataMd5(ruleId: Long, rowDataMd5: String): List<RuleResultEntity>

    // find by rule id and businessKeyMd5
    fun findByRuleIdAndBusinessKeyDataMd5(ruleId: Long, businessKeyDataMd5: String): List<RuleResultEntity>

    // find by rule id
    fun findByRuleId(ruleId: Long): List<RuleResultEntity>

    // find by ruleId eq ? and runId not eq ? and isFixed eq 0
    fun findByRuleIdAndRunIdIsNotAndIsFixed(ruleId: Long, runId: String, isFixed: Int): List<RuleResultEntity>

}
