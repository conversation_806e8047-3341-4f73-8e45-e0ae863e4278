package com.datayes.spring

import com.datayes.domain.RuleExecutionLogEntity
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@SpringBootTest
@Transactional // 使用事务，测试后自动回滚
class RuleExecutionLogRepositoryIntegrationTest {

    @Autowired
    private lateinit var ruleExecutionLogRepository: RuleExecutionLogRepository

    @Test
    fun `should find RuleExecutionLogEntity by id without exception`() {
        // Given: Create and save an entity
        val entityToSave = RuleExecutionLogEntity(
            ruleId = 1L, // Provide necessary fields
            taskId = 101L,
            runId = "test-run-123",
            level = "INFO",
            message = "Test log message",
            host = "localhost",
            timestamp = LocalDateTime.now(),
            dbHost = "localhost",
            dbName = "testdb",
            dbUsername = "testuser",
            sqlStatus = "SUCCESS"
        )
        val savedEntity = ruleExecutionLogRepository.save(entityToSave)

        // When: Find the entity by its ID
        val foundEntityOptional = ruleExecutionLogRepository.findById(savedEntity.id!!)

        // Then: Assert that the entity was found and no exception occurred
        assertNotNull(foundEntityOptional.orElse(null), "Entity should be found by ID")
    }
}
