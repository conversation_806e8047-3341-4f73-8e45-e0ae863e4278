package com.datayes.executor

import com.datayes.domain.RuleResultEntity
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.zaxxer.hikari.HikariDataSource
import org.slf4j.LoggerFactory
import java.security.MessageDigest
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import javax.sql.DataSource

data class CancelContext(val cancelFlag: AtomicBoolean = AtomicBoolean(false))

interface QueryExecutor {
    fun queryPage(pagedSql: String): List<Map<String, Any?>>

    fun queryCount(countSql: String): Long
}

class JdbcQueryExecutor(private val dataSource: DataSource) : QueryExecutor {

    private val logger = LoggerFactory.getLogger(JdbcQueryExecutor::class.java)

    override fun queryPage(pagedSql: String): List<Map<String, Any?>> {
        val isHive = if (dataSource is HikariDataSource) dataSource.jdbcUrl.contains("hive") else false
        dataSource.connection.use { conn ->
            conn.createStatement().use { stmt ->
                stmt.executeQuery(pagedSql).use { rs ->
                    val meta = rs.metaData
                    val columnCount = meta.columnCount
                    val result = mutableListOf<Map<String, Any?>>()
                    while (rs.next()) {
                        val row = mutableMapOf<String, Any?>()
                        val columnNames = mutableSetOf<String>()

                        // First pass: collect all column names to detect conflicts
                        for (i in 1..columnCount) {
                            val columnName = meta.getColumnLabel(i)
                            columnNames.add(columnName)
                        }

                        // Count occurrences of each column name
                        val columnNameCount = columnNames.associateWith { name ->
                            (1..columnCount).count { i -> meta.getColumnLabel(i) == name }
                        }

                        // Second pass: add values to the row
                        for (i in 1..columnCount) {
                            val tableName = if (isHive) null else meta.getTableName(i)
                            val columnName = meta.getColumnLabel(i)

                            // Only add tableName prefix if there's a column name conflict
                            val key = if (columnNameCount[columnName]!! > 1 && tableName.isNullOrBlank().not())
                                "$tableName.$columnName"
                            else
                                columnName

                            row[key] = rs.getObject(i)
                        }
                        result.add(row)
                    }
                    return result
                }
            }
        }
    }

    override fun queryCount(countSql: String): Long {
        val connection = dataSource.connection
        connection.use { conn ->
            val statement = conn.createStatement()
            statement.use { stmt ->
                val resultSet = stmt.executeQuery(countSql)
                resultSet.use { rs ->
                    return if (rs.next()) {
                        rs.getInt(1).toLong()
                    } else {
                        0L
                    }
                }
            }
        }
    }
}

object DatabaseExecutor {

    private val log = LoggerFactory.getLogger(DatabaseExecutor::class.java)

    private val cancelContextMap: MutableMap<Long, CancelContext> = ConcurrentHashMap()

    private val objectMapper = jacksonObjectMapper().registerModule(JavaTimeModule()).registerKotlinModule()

    fun executePagedAndSave(
        dbType: String,
        baseSql: String,
        ruleId: Long,
        runId: String,
        cancelContext: CancelContext,
        queryExecutor: QueryExecutor,
        resultBatchProcessor: ResultBatchProcessor,
        pageSize: Int,
        businessKeyList: List<String>,
    ): Int {
        var offset = 0
        var rowIndex = 0

        try {
            var continuePaging = true
            while (continuePaging) {
                if (cancelContext.cancelFlag.get()) break

                val pagedSql = buildPagedSql(dbType, baseSql, offset, pageSize)
                log.info("af6cf59a | Executing SQL: $pagedSql with page size: $pageSize and offset: $offset")

                val startTime = System.currentTimeMillis()
                val executedAt = LocalDateTime.now()

                val rows = queryExecutor.queryPage(pagedSql)
                val executionTimeMs = System.currentTimeMillis() - startTime
                log.info("c6a6ef28 | $pagedSql | Execution time: ${executionTimeMs}ms")

                val batch = rows.map { row ->
                    val rowData = objectMapper.writeValueAsString(row)

                    val businessKeyData = extractBusinessKey(row, businessKeyList)
                    val businessKeyDataMd5 = md5Hex(objectMapper.writeValueAsString(businessKeyData))

                    RuleResultEntity(
                        ruleId = ruleId,
                        dbType = dbType,
                        originalSql = baseSql,
                        runId = runId,
                        executedAt = executedAt,
                        rowData = rowData,
                        businessKeyData = objectMapper.writeValueAsString(businessKeyData),
                        businessKeyDataMd5 = businessKeyDataMd5,
                        rowIndex = rowIndex++,
                        isFixed = 0,
                        executionTimeMs = executionTimeMs
                    )
                }

                if (batch.isNotEmpty()) {
                    val batchStart = System.currentTimeMillis()
                    resultBatchProcessor.process(batch)
                    val batchEnd = System.currentTimeMillis()
                    log.info("1fdffb8e | Batch processing time: ${batchEnd - batchStart}ms")
                }

                continuePaging = batch.size == pageSize
                offset += pageSize
            }
        } finally {
            cancelContextMap.remove(ruleId)
        }

        return rowIndex
    }

    // extract business key from row
    fun extractBusinessKey(row: Map<String, Any?>, businessKeyList: List<String>): Map<String, Any?> {
        if (businessKeyList.isEmpty()) {
            return row
        }
        return businessKeyList.associateWith { key -> row[key] }
    }

    fun buildPagedSql(dbType: String, baseSql: String, offset: Int, pageSize: Int): String {

        // 先检查baseSql是否为空
        if (baseSql.isEmpty() || baseSql.isBlank()) {
            throw IllegalArgumentException("e1538aba | sql不能为空")
        }

        // 移除末尾的分号
        val cleanBaseSql = baseSql.trimEnd().removeSuffix(";")

        // 检查SQL是否已经包含LIMIT子句且offset为0
        if (cleanBaseSql.uppercase().contains(" LIMIT ") && offset == 0) {
            return cleanBaseSql
        }

        return when (dbType.lowercase()) {
            "mysql", "tdsql" -> "$cleanBaseSql LIMIT $pageSize OFFSET $offset"
            "postgresql", "tbase" -> "$cleanBaseSql LIMIT $pageSize OFFSET $offset"
            "oracle" -> """
                SELECT * FROM (
                    SELECT a.*, ROWNUM rnum FROM (
                        $cleanBaseSql
                    ) a WHERE ROWNUM <= ${offset + pageSize}
                ) WHERE rnum > $offset
            """.trimIndent()

            "hive" -> "$cleanBaseSql LIMIT $pageSize OFFSET $offset"
            else -> throw IllegalArgumentException("Unsupported dbType: $dbType")
        }
    }

    fun cancelRun(ruleId: Long): Boolean {
        val context = cancelContextMap[ruleId] ?: return false
        context.cancelFlag.set(true)
        return true
    }

    fun md5Hex(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray())
        return digest.joinToString("") { "%02x".format(it) }
    }
}

/**
 * Modify Hive JDBC URL to include database name
 * Handles both standard URLs and ZooKeeper service discovery URLs
 */
fun modifyHiveUrlForDatabase(jdbcUrl: String, databaseName: String): String {
    return when {
        // Handle ZooKeeper service discovery URLs like:
        // ***********************,host2:2181,host3:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2
        jdbcUrl.contains("serviceDiscoveryMode=zooKeeper") -> {
            // Replace the empty path "/" with "/$databaseName"
            jdbcUrl.replace("/;", "/$databaseName;")
        }
        // Handle standard Hive URLs like:
        // ***********************/
        jdbcUrl.endsWith("/") -> {
            jdbcUrl + databaseName
        }
        // Handle URLs that already have a database but we want to replace it
        jdbcUrl.matches(Regex("jdbc:hive2://[^/]+/[^;?]*.*")) -> {
            // Replace existing database with new one
            jdbcUrl.replaceFirst(Regex("(jdbc:hive2://[^/]+/)[^;?]*"), "$1$databaseName")
        }
        // If URL doesn't match expected patterns, append database with separator
        else -> {
            if (jdbcUrl.contains("?") || jdbcUrl.contains(";")) {
                // URL already has parameters, add database before them
                val parts = jdbcUrl.split(Regex("[?;]"), 2)
                "${parts[0]}/$databaseName;${parts[1]}"
            } else {
                // Simple URL, just append database
                "$jdbcUrl/$databaseName"
            }
        }
    }
}

/**
 * Ensure Hive JDBC URL contains required ZooKeeper parameters
 * Handles various URL formats and avoids parameter duplication
 */
fun ensureHiveZooKeeperParams(jdbcUrl: String): String {
    if (jdbcUrl.isBlank()) {
        throw IllegalArgumentException("JDBC URL cannot be blank")
    }

    // Check if URL already has all required ZooKeeper parameters
    val hasServiceDiscovery = jdbcUrl.contains("serviceDiscoveryMode=zooKeeper")
    val hasNamespace = jdbcUrl.contains("zooKeeperNamespace=hiveserver2")

    if (hasServiceDiscovery && hasNamespace) {
        return jdbcUrl
    }

    // Parse URL to determine how to add missing parameters
    val requiredParams = mutableListOf<String>()
    if (!hasServiceDiscovery) {
        requiredParams.add("serviceDiscoveryMode=zooKeeper")
    }
    if (!hasNamespace) {
        requiredParams.add("zooKeeperNamespace=hiveserver2")
    }

    return when {
        // URL already has parameters (contains ; or ?)
        jdbcUrl.contains(";") || jdbcUrl.contains("?") -> {
            val separator = if (jdbcUrl.contains(";")) ";" else "&"
            "$jdbcUrl$separator${requiredParams.joinToString(separator)}"
        }
        // URL ends with database path but no parameters
        jdbcUrl.matches(Regex(".*/.+$")) -> {
            "$jdbcUrl;${requiredParams.joinToString(";")}"
        }
        // URL ends with / - add parameters after /
        jdbcUrl.endsWith("/") -> {
            "$jdbcUrl;${requiredParams.joinToString(";")}"
        }
        // URL has no path separator - add /; first
        else -> {
            "$jdbcUrl;${requiredParams.joinToString(";")}"
        }
    }
}

private val logger = LoggerFactory.getLogger(DatabaseExecutor::class.java)

fun createDataSource(
    dbType: String,
    jdbcUrl: String,
    username: String,
    password: String,
    ruleName: String,
    databaseName: String? = null
): DataSource {
    return HikariDataSource().apply {
        // For Hive with custom JDBC URL, modify URL to include database name if needed
        val finalJdbcUrl = if (dbType.lowercase() == "hive" && !databaseName.isNullOrBlank()) {
            val modifiedUrl = modifyHiveUrlForDatabase(jdbcUrl, databaseName)
            if (modifiedUrl != jdbcUrl) {
                logger.info("Modified Hive JDBC URL to include database '$databaseName': $modifiedUrl")
            }

            // *****************************,10.9.112.26:2181,10.9.112.32:2181 -> *****************************,10.9.112.26:2181,10.9.112.32:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2
            ensureHiveZooKeeperParams(modifiedUrl)
        } else {
            jdbcUrl
        }

        this.jdbcUrl = finalJdbcUrl
        this.username = username
        this.password = password
        driverClassName = when (dbType.lowercase()) {
            "mysql", "tdsql" -> "com.mysql.cj.jdbc.Driver"
            "postgresql", "tbase" -> "org.postgresql.Driver"
            "oracle" -> "oracle.jdbc.OracleDriver"
            "hive" -> "org.apache.hive.jdbc.HiveDriver"
            else -> throw IllegalArgumentException("Unsupported dbType: $dbType")
        }

        // Set catalog/schema if database name is provided (for non-Hive databases)
        if (dbType.lowercase() != "hive") {
            databaseName?.let { dbName ->
                when (dbType.lowercase()) {
                    "mysql", "tdsql" -> {
                        // For MySQL, set catalog
                        catalog = dbName
                    }

                    "postgresql", "tbase" -> {
                        // For PostgreSQL, set schema (database is already in URL)
                        schema = dbName
                    }

                    "oracle" -> {
                        // For Oracle, set schema
                        schema = dbName
                    }
                }
            }
        }

        maximumPoolSize = 1
        minimumIdle = 1
        isAutoCommit = true
        poolName = "TaskConsumer-$ruleName"
    }
}
