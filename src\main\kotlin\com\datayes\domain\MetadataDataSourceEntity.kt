package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("metadata_data_source")
data class MetadataDataSourceEntity(

    @Id
    val id: Long? = null,

    @Column("SOURCE_NAME")
    val sourceName: String,

    @Column("DB_TYPE")
    val dbType: String,

    @Column("DB_DRIVER")
    val dbDriver: String,

    @Column("DB_NAME")
    val dbName: String,

    @Column("DB_URL")
    val dbUrl: String?,

    @Column("DB_PORT")
    val dbPort: Int?,

    @Column("DB_USERNAME")
    val dbUsername: String,

    @Column("DB_PASSWORD")
    val dbPassword: String,

    @Column("CUSTOM_JDBC_URL")
    val customJdbcUrl: String? = null,

    @Column("ACTIVE_FLAG")
    val activeFlag: Boolean = true,

    @Column("CREATE_BY")
    val createBy: String? = null,

    @Column("CREATE_TIME")
    val createTime: LocalDateTime? = null,

    @Column("UPDATE_BY")
    val updateBy: String? = null,

    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime? = null
) 