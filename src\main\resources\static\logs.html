<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        body { 
            font-family: monospace; 
            background: #222; 
            color: #eee; 
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            margin: 0 auto;
        }
        h1 {
            margin-top: 0;
            color: #4CAF50;
        }
        .log-entry { 
            border-bottom: 1px solid #444; 
            padding: 8px 0;
            white-space: pre-wrap;
        }
        .toolbar { 
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            background: #333;
            padding: 10px;
            border-radius: 4px;
            align-items: center;
            flex-wrap: wrap;
        }
        input, button { 
            font-size: 1em; 
            padding: 8px;
            border-radius: 4px;
            border: none;
        }
        input {
            flex-grow: 1;
            background: #444;
            color: #fff;
        }
        button {
            background: #4CAF50;
            color: white;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        .clear-btn {
            background: #f44336;
        }
        .clear-btn:hover {
            background: #d32f2f;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        #log-list {
            background: #333;
            border-radius: 4px;
            padding: 10px;
            max-height: 70vh;
            overflow-y: auto;
        }
        .log-warn { color: #FFB700; /* Amber */ }
        .log-error { color: #FF6B6B; /* Soft Red */ }
    </style>
    <script>
    // 获取应用程序的上下文路径
    function getContextPath() {
        const path = window.location.pathname;
        const contextPath = path.substring(0, path.lastIndexOf('/'));
        return contextPath || '';
    }
    
    // 初始化时处理所有HTMX路径
    document.addEventListener('DOMContentLoaded', function() {
        const contextPath = getContextPath();
        console.log('Context path:', contextPath);
        
        // 为所有具有hx-get属性的元素添加上下文路径
        document.querySelectorAll('[hx-get]').forEach(el => {
            const currentPath = el.getAttribute('hx-get');
            if (currentPath.startsWith('/')) {
                el.setAttribute('hx-get', contextPath + currentPath);
            }
        });
    });
    </script>
</head>
<body x-data="{ query: '', autoRefresh: false, interval: null }" x-init="
    $watch('autoRefresh', value => {
        if(value) {
            interval = setInterval(() => { $refs.searchBtn.click() }, 3000)
        } else {
            clearInterval(interval)
        }
    })
" x-on:htmx:config-request.window="
    if (event.detail.elt.id === 'searchBtn') {
        event.detail.parameters.query = query;
    }
">
    <div class="container">
        <h1>日志查看器</h1>
        <div class="toolbar">
            <input type="text" x-model="query" placeholder="搜索日志..." name="query"
                   @keyup.enter="$refs.searchBtn.click()"/>
            <button 
                id="searchBtn"
                x-ref="searchBtn"
                hx-get="/logs/fragment"
                hx-trigger="click"
                hx-target="#log-list"
                hx-indicator="#loading"
            >搜索</button>
            <button class="clear-btn"
                hx-get="/api/logs/clear" 
                hx-trigger="click"
                hx-target="#log-list" 
                hx-swap="innerHTML"
                hx-indicator="#loading"
            >清空日志</button>
            <div class="control-group">
                <input type="checkbox" id="auto-refresh" x-model="autoRefresh" />
                <label for="auto-refresh">自动刷新</label>
            </div>
            <div id="loading" class="loading" style="display:none;"></div>
        </div>
        <div id="log-list" hx-get="/logs/fragment" hx-trigger="load" hx-swap="innerHTML" hx-on:htmx:after-swap="colorizeLogList(event.detail.target)">
            <!-- 日志内容将被 HTMX 替换 -->
        </div>
    </div>
<script>
function colorizeLogList(target) {
    console.log("colorizeLogList", target);
    if (target && target.id === 'log-list') {
        // 优先处理已拥有 .log-entry 类的元素
        let entriesToProcess = Array.from(target.querySelectorAll('.log-entry'));

        if (entriesToProcess.length === 0 && target.children.length > 0) {
            // 回退方案：如果服务器未添加 .log-entry，则处理直接子元素
            // 这可能意味着 .log-entry 的原始样式也未应用于各行日志
            // 这是一种尽力而为的着色尝试
            entriesToProcess = Array.from(target.children);
        }

        entriesToProcess.forEach(entry => {
            const upperText = (entry.textContent || entry.innerText || "").toUpperCase();
            
            // 重置类名，以处理可能已更改或不再匹配的日志条目
            entry.classList.remove('log-warn', 'log-error');

            // 使用正则表达式   来匹配完整的单词 "ERROR" 或 "WARN"
            if (/ERROR/.test(upperText)) { // 如果同时出现，优先标记 ERROR
                entry.classList.add('log-error');
            } else if (/WARN/.test(upperText)) {
                entry.classList.add('log-warn');
            }
        });
    }
}

// HTMX 请求拦截器 - 动态添加上下文路径
document.addEventListener('htmx:configRequest', function(event) {
    const contextPath = getContextPath();
    const path = event.detail.path;
    
    // 如果路径以/开头且不包含完整URL
    if (path && path.startsWith('/') && !path.startsWith('http')) {
        event.detail.path = contextPath + path;
    }
});
</script>
</body>
</html> 