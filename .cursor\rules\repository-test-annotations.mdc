---
description: Repository Test Annotations
globs: 
alwaysApply: false
---
# Repository Test Annotations
    
Repository测试类应该使用 `@DataJdbcTest` 和 `@AutoConfigureTestDatabase` 注解的组合，而不是 `@SpringBootTest`。

正确的注解方式：
```kotlin
@DataJdbcTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class SomeRepositoryTest {
    // 测试代码
}
```

## 原因

1. **测试范围更精确**：`@DataJdbcTest` 专门针对数据访问层进行测试，只加载与Spring Data JDBC相关的组件，启动更快、资源消耗更少。

2. **关注点分离**：Repository测试只需要验证数据访问功能，不需要加载整个应用上下文。

3. **测试隔离性**：`@DataJdbcTest` 默认会为每个测试方法回滚事务，保证测试之间互不影响。

4. **真实数据库**：`@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)` 确保测试使用真实数据库而不是内存数据库。

## 示例参考

参考实现：[MetadataDataSourceRepositoryTest.kt](mdc:src/test/kotlin/com/datayes/spring/MetadataDataSourceRepositoryTest.kt)

