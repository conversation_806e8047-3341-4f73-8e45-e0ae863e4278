@file:Suppress("SpringJavaInjectionPointsAutowiringInspection")

package com.datayes.spring.kafka

import com.datayes.domain.*
import com.datayes.executor.*
import com.datayes.spring.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.dao.OptimisticLockingFailureException
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import javax.annotation.PreDestroy
import javax.sql.DataSource
import kotlin.jvm.optionals.getOrNull
import kotlin.math.abs
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeoutException

@Component
class TaskConsumer(
    private val issueRepository: IssueRepository,
    private val ruleResultRepository: RuleResultRepository,
    private val resultBatchProcessor: ResultBatchProcessor,
    private val issueRunHistoryRepository: IssueRunHistoryRepository,
    private val metadataDataSourceRepository: MetadataDataSourceRepository,
    private val ruleExecutionLogRepository: RuleExecutionLogRepository, // Inject the new repository
    private val hostInfo: HostInfo, // Inject HostInfo
) {

    private val log = LoggerFactory.getLogger(TaskConsumer::class.java)

    // Executor for periodic logging and cleanup tasks
    private val scheduler: ScheduledExecutorService = Executors.newScheduledThreadPool(2)
    
    // Executor for handling message processing with timeout (dedicated thread pool)
    private val messageTimeoutExecutor: ScheduledExecutorService = Executors.newScheduledThreadPool(10)

    // Hard timeout configuration for message processing (in minutes)
    @Value("\${app.message.processing.timeout.minutes:30}")
    private val messageTimeoutMinutes: Long = 30L // 默认30分钟超时 (Default 30 minutes timeout)

    // Shutdown flag to prevent new task processing during shutdown
    private val isShuttingDown = AtomicBoolean(false)

    // Track currently processing messages for cleanup
    private val activeProcessingTasks = ConcurrentHashMap<String, TaskProcessingContext>()

    // Data class to track active task processing context
    data class TaskProcessingContext(
        val taskId: Long,
        val runId: String,
        val acquiredIssues: MutableSet<Long> = mutableSetOf(),
        val startTime: LocalDateTime = LocalDateTime.now(),
        var timeoutFuture: ScheduledFuture<*>? = null  // 添加超时任务引用 (Add timeout task reference)
    )

    init {
        // Schedule periodic cleanup of stale RUNNING issues every 30 minutes
        // Use host identifier to distribute cleanup timing across replicas
        val cleanupDelayMinutes = 30L + (hostInfo.hostIdentifier.hashCode() % 10).toLong()
        scheduler.scheduleAtFixedRate({
            try {
                log.debug("Running periodic cleanup of stale RUNNING issues on host: ${hostInfo.hostIdentifier}")
                cleanupStaleRunningIssuesDistributed()
            } catch (e: Exception) {
                log.error("Error during scheduled cleanup of stale issues", e)
            }
        }, cleanupDelayMinutes, 30, TimeUnit.MINUTES) // Staggered start, then every 30 minutes
    }

    private fun logExecutionStep(
        runId: String,
        ruleId: Long,
        taskId: Long,
        level: String,
        message: String,
        dbHost: String? = null,
        dbName: String? = null,
        dbUsername: String? = null,
        sqlStatus: String? = null,
    ) {
        try {
            val logEntry = RuleExecutionLogEntity(
                runId = runId,
                ruleId = ruleId,
                taskId = taskId,
                timestamp = LocalDateTime.now(),
                level = level,
                message = message,
                host = hostInfo.hostIdentifier,
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = sqlStatus
            )
            ruleExecutionLogRepository.save(logEntry)
        } catch (e: Exception) {
            // Log to application logs if saving to DB fails
            log.error("Failed to save execution log for runId=$runId, ruleId=$ruleId, taskId=$taskId: $message", e)
        }
    }

    // this app will be set scaled to multiple instances
    // they share the same consumer group
    @KafkaListener(
        topics = ["data-quality-tasks"],
        groupId = "executor-group-task",
        containerFactory = "taskKafkaListenerContainerFactory"
    )
    fun onMessage(message: TaskMessage, acknowledgment: Acknowledgment) {
        // Check if we're shutting down
        if (isShuttingDown.get()) {
            log.warn("Rejecting new message during shutdown: taskId=${message.taskId}, runId=${message.runId}")
            // 不确认消息，让其他节点处理 (Don't acknowledge message, let other nodes handle it)
            return
        }

        log.info("a548fcf6 | Received message: $message")
        log.info("fd21c5a3 | post tasks size: ${message.postTasks.size}")

        // Track this processing task
        val processingKey = "${message.taskId}-${message.runId}"
        val processingContext = TaskProcessingContext(message.taskId, message.runId)
        activeProcessingTasks[processingKey] = processingContext

        // 创建带超时的消息处理 (Create message processing with timeout)
        val messageProcessingFuture = CompletableFuture.supplyAsync {
            processMessageWithLogging(message, processingContext)
        }

        // 设置硬超时任务 (Set hard timeout task)
        val timeoutFuture = messageTimeoutExecutor.schedule({
            if (!messageProcessingFuture.isDone) {
                log.error("Message processing timed out after ${messageTimeoutMinutes} minutes: taskId=${message.taskId}, runId=${message.runId}")
                
                // 记录超时错误到所有规则的执行日志 (Log timeout error for all rules)
                message.rules.forEach { rule ->
                    logExecutionStep(
                        message.runId,
                        rule.ruleId,
                        message.taskId,
                        "ERROR",
                        "消息处理超时，已强制结束处理 (Message processing timed out, forcefully terminated)"
                    )
                    
                    // 尝试将规则相关的问题标记为错误状态 (Try to mark rule-related issues as error)
                    try {
                        markRuleAsTimedOut(rule.ruleId, message.taskId, message.runId)
                    } catch (e: Exception) {
                        log.error("Failed to mark rule ${rule.ruleId} as timed out", e)
                    }
                }
                
                // 强制完成Future，表示处理已超时 (Force complete the Future to indicate timeout)
                messageProcessingFuture.complete(false)
            }
        }, messageTimeoutMinutes, TimeUnit.MINUTES)

        // 保存超时任务引用 (Save timeout task reference)
        processingContext.timeoutFuture = timeoutFuture

        try {
            // 等待消息处理完成或超时 (Wait for message processing to complete or timeout)
            val processingResult = messageProcessingFuture.get()
            
            // 取消超时任务（如果还未执行） (Cancel timeout task if not yet executed)
            timeoutFuture.cancel(false)
            
            if (processingResult) {
                log.info("3da5f9f3 | Message processing completed successfully: taskId=${message.taskId}, taskName=${message.taskName}, runId=${message.runId}")
            } else {
                log.warn("Message processing failed or timed out: taskId=${message.taskId}, taskName=${message.taskName}, runId=${message.runId}")
            }
            
        } catch (e: Exception) {
            log.error("Error during message processing: taskId=${message.taskId}, runId=${message.runId}", e)
            timeoutFuture.cancel(false)
        } finally {
            // Clean up processing context and any acquired issues if shutdown is in progress
            val context = activeProcessingTasks.remove(processingKey)

            if (isShuttingDown.get() && context != null) {
                cleanupAcquiredIssues(context)
            }

            // 消息处理成功，手动确认 (Message processed successfully, manual acknowledgment)
            acknowledgment.acknowledge()
            log.info("Message acknowledged: taskId=${message.taskId}, taskName=${message.taskName}, runId=${message.runId}")
        }
    }

    /**
     * 处理消息并记录日志 (Process message with logging)
     * 将原来的 onMessage 逻辑封装到单独的方法中
     */
    private fun processMessageWithLogging(message: TaskMessage, processingContext: TaskProcessingContext): Boolean {
        // Schedule a task to log the current message being processed every 10 seconds
        val loggerTask = scheduler.scheduleAtFixedRate({
            log.info("76543ef5 | Currently processing message: taskId=${message.taskId}, taskName=${message.taskName}, runId=${message.runId}")
        }, 10, 10, TimeUnit.SECONDS)

        try {
            var hasResults = false
            val runId = message.runId

            for (rule in message.rules) {
                try {
                    val ret = processRule(rule, message)
                    ret.onSuccess {
                        if (it > 0) {
                            log.info("ab66d48e | Rules execution completed with results, processing post tasks, ruleId=${rule.ruleId}, taskId=${message.taskId}, taskName=${message.taskName}")
                            hasResults = true
                        }
                    }.onFailure {
                        log.error("d4594f59 | Error processing rule ${rule.ruleId}: ${it.message}", it)
                    }
                } catch (e: Exception) {
                    log.error("3188a7e5 | Error processing rule ${rule.ruleId}: ${e.message}", e)
                    // 继续处理其他规则
                }
            }

            // 如果有结果，执行后续任务
            if (hasResults) {
                log.info("35ae1e96 | Rules execution completed with results, processing post tasks")
                for (postTask in message.postTasks) {
                    try {
                        processPostTask(postTask, runId)
                    } catch (e: Exception) {
                        log.error("Error processing post task ${postTask.taskId}: ${e.message}", e)
                    }
                }
            } else {
                log.info("2ad9fb7e | No results from rules execution, skipping post tasks | $runId")
            }
            
            return true // 处理成功 (Processing successful)
        } finally {
            // Cancel the periodic logging task
            loggerTask.cancel(false)
        }
    }

    /**
     * 将规则标记为超时状态 (Mark rule as timed out)
     * 更新相关的问题实体和运行历史
     */
    private fun markRuleAsTimedOut(ruleId: Long, taskId: Long, runId: String) {
        try {
            // 查找当前正在运行的问题 (Find currently running issue)
            val runningIssue = issueRepository.findByRuleIdAndTaskIdAndCurrentStage(
                ruleId, taskId, IssueStage.RUNNING
            )
            
            if (runningIssue != null) {
                // 更新问题状态为超时错误 (Update issue status to timeout error)
                val timeoutIssue = runningIssue.copy(
                    currentStage = IssueStage.ERROR,
                    updateBy = "SYSTEM_TIMEOUT_${hostInfo.hostIdentifier}",
                    updatedAt = LocalDateTime.now()
                )
                issueRepository.save(timeoutIssue)
                
                // 更新运行历史为超时状态 (Update run history to timeout status)
                val runHistory = issueRunHistoryRepository.findByIssueIdAndRunId(runningIssue.id!!, runId)
                if (runHistory != null) {
                    val timeoutHistory = runHistory.copy(
                        status = RunStatus.TIMEOUT,
                        endTime = LocalDateTime.now(),
                        errorMessage = "消息处理超时，已强制结束 (Message processing timed out, forcefully terminated)"
                    )
                    issueRunHistoryRepository.save(timeoutHistory)
                }
                
                log.info("Marked rule as timed out: ruleId=$ruleId, taskId=$taskId, runId=$runId")
            }
        } catch (e: Exception) {
            log.error("Failed to mark rule as timed out: ruleId=$ruleId, taskId=$taskId, runId=$runId", e)
        }
    }

    /**
     * Try to create a new issue entity and set it status to running,
     * or query the existing entity which status can't been running and update it to running.
     * Also cleans up any stale RUNNING issues that have been running for too long.
     *
     * @param ruleId the rule id
     * @return the issue entity if success, otherwise return null
     */
    private fun acquireIssueForRunning(ruleId: Long, taskId: Long): IssueEntity? {
        // Clean up stale RUNNING issues before attempting to acquire lock
        cleanupStaleRunningIssues()

        return acquireIssueWithRetry(ruleId, taskId, maxRetries = 3)
    }

    /**
     * Acquire issue with exponential backoff retry to reduce database contention in K8s multi-replica environment
     */
    private fun acquireIssueWithRetry(ruleId: Long, taskId: Long, maxRetries: Int = 3): IssueEntity? {
        var attempt = 0
        while (attempt < maxRetries) {
            try {
                return attemptAcquireIssue(ruleId, taskId)
            } catch (e: OptimisticLockingFailureException) {
                attempt++
                if (attempt >= maxRetries) {
                    log.info("Failed to acquire issue after $maxRetries attempts for ruleId=$ruleId, taskId=$taskId")
                    return null
                }

                // Exponential backoff with jitter to reduce thundering herd
                val baseDelay = 100L * (1L shl (attempt - 1)) // 100ms, 200ms, 400ms
                val jitter = (0..50).random().toLong() // Add 0-50ms jitter
                val delayMs = baseDelay + jitter

                log.debug("Lock acquisition failed for ruleId=$ruleId, attempt $attempt/$maxRetries, retrying in ${delayMs}ms")
                Thread.sleep(delayMs)
            } catch (e: DataIntegrityViolationException) {
                log.info("Issue for rule id = $ruleId was created by another node during acquisition")
                return null
            } catch (e: Exception) {
                log.error("Unexpected error during issue acquisition for ruleId=$ruleId", e)
                return null
            }
        }
        return null
    }

    /**
     * Single attempt to acquire an issue for running
     */
    private fun attemptAcquireIssue(ruleId: Long, taskId: Long): IssueEntity? {
        // val existingIssues = issueRepository.findByRuleId(ruleId)
        val existingIssues = issueRepository.findByRuleIdAndTaskId(ruleId, taskId)
        if (existingIssues.size > 1) {
            log.error("8d952143 | Multiple issues found for ruleId={}, aborting task.", ruleId)
            return null
        }

        if (existingIssues.isNotEmpty()) {
            val existingIssue = existingIssues.single()

            // 检查任务状态，如果已经在运行中则跳过
            if (existingIssue.currentStage == IssueStage.RUNNING) {
                log.info("27971690 | Task for rule id = {} is already running on another node, skipping", ruleId)
                return null
            }

            // Allow retrying CRASHED or non-RUNNING issues
            // 尝试更新状态为RUNNING，如果失败说明被其他节点抢先处理了
            val updatedIssue = existingIssue.copy(
                currentStage = IssueStage.RUNNING,
                updateBy = "SYSTEM",
                updatedAt = LocalDateTime.now()
            )
            val savedIssue = issueRepository.save(updatedIssue)

            // Track the acquired issue for cleanup during shutdown
            trackAcquiredIssue(ruleId, taskId, savedIssue.id!!)

            return savedIssue
        }

        val newIssue = IssueEntity(
            ruleId = ruleId,
            taskId = taskId,
            currentStage = IssueStage.RUNNING,
            createBy = "SYSTEM",
            createdAt = LocalDateTime.now(),
        )
        val savedIssue = issueRepository.save(newIssue)

        // Track the acquired issue for cleanup during shutdown
        trackAcquiredIssue(ruleId, taskId, savedIssue.id!!)

        return savedIssue
    }

    /**
     * Cleanup stale RUNNING issues that may have been left orphaned due to node crashes.
     * Issues that have been in RUNNING state for more than the specified timeout will be
     * reset to CRASHED state so they can be retried.
     *
     * @param maxDurationMinutes Maximum time in minutes an issue can stay in RUNNING state
     */
    private fun cleanupStaleRunningIssues(maxDurationMinutes: Long = 60) {
        try {
            val cutoffTime = LocalDateTime.now().minusMinutes(maxDurationMinutes)
            val staleIssues = issueRepository.findByCurrentStageAndUpdatedAtBefore(
                IssueStage.RUNNING, cutoffTime
            )

            if (staleIssues.isNotEmpty()) {
                log.warn("Found ${staleIssues.size} stale RUNNING issues, marking them as CRASHED for cleanup")

                staleIssues.forEach { staleIssue ->
                    try {
                        val crashedIssue = staleIssue.copy(
                            currentStage = IssueStage.CRASHED,
                            updateBy = "SYSTEM_CLEANUP",
                            updatedAt = LocalDateTime.now()
                        )
                        issueRepository.save(crashedIssue)
                        log.info("Marked stale issue as CRASHED: ruleId=${staleIssue.ruleId}, taskId=${staleIssue.taskId}, issueId=${staleIssue.id}")
                    } catch (e: Exception) {
                        log.error(
                            "Failed to cleanup stale issue: ruleId=${staleIssue.ruleId}, taskId=${staleIssue.taskId}",
                            e
                        )
                    }
                }
            }
        } catch (e: Exception) {
            log.error("Error during stale issue cleanup", e)
        }
    }

    /**
     * Distributed cleanup method optimized for K8s multi-replica deployment.
     * Uses host identifier to coordinate cleanup between replicas and reduce database contention.
     */
    private fun cleanupStaleRunningIssuesDistributed(maxDurationMinutes: Long = 60) {
        try {
            val cutoffTime = LocalDateTime.now().minusMinutes(maxDurationMinutes)

            // Use host identifier hash to determine which issues this pod should clean up
            // This distributes cleanup work across replicas and reduces database contention
            val hostHash = hostInfo.hostIdentifier.hashCode()

            val allStaleIssues = issueRepository.findByCurrentStageAndUpdatedAtBefore(
                IssueStage.RUNNING, cutoffTime
            )

            // Filter issues by host responsibility using modulo operation
            // Each pod only handles issues where (ruleId + taskId) % totalEstimatedPods == thisPodsPosition
            val podResponsibleIssues = allStaleIssues.filter { issue ->
                val issueHash = (issue.ruleId + issue.taskId).hashCode()
                abs(issueHash % 5) == abs(hostHash % 5) // Assuming 5 replicas, adjust as needed
            }

            if (podResponsibleIssues.isNotEmpty()) {
                log.warn("Host ${hostInfo.hostIdentifier} cleaning up ${podResponsibleIssues.size} out of ${allStaleIssues.size} stale RUNNING issues")

                podResponsibleIssues.forEach { staleIssue ->
                    try {
                        val crashedIssue = staleIssue.copy(
                            currentStage = IssueStage.CRASHED,
                            updateBy = "SYSTEM_CLEANUP_${hostInfo.hostIdentifier}",
                            updatedAt = LocalDateTime.now()
                        )
                        issueRepository.save(crashedIssue)
                        log.info("Host ${hostInfo.hostIdentifier} marked stale issue as CRASHED: ruleId=${staleIssue.ruleId}, taskId=${staleIssue.taskId}, issueId=${staleIssue.id}")
                    } catch (e: Exception) {
                        log.error(
                            "Failed to cleanup stale issue: ruleId=${staleIssue.ruleId}, taskId=${staleIssue.taskId}",
                            e
                        )
                    }
                }
            } else if (allStaleIssues.isNotEmpty()) {
                log.debug("Host ${hostInfo.hostIdentifier} found ${allStaleIssues.size} stale issues but none assigned to this pod")
            }
        } catch (e: Exception) {
            log.error("Error during distributed stale issue cleanup on host ${hostInfo.hostIdentifier}", e)
        }
    }

    private fun buildJdbcUrl(dbType: String, dbUrl: String, dbName: String, dbPort: Int): String {
        // 如果已经是完整的 JDBC URL，直接返回
        if (dbUrl.startsWith("jdbc:")) {
            return dbUrl
        }

        // 根据数据库类型构建 JDBC URL
        return when (dbType.lowercase()) {
            "mysql", "tdsql" -> "****************************************************************************************"
            "hive" -> "***********************************"
            else -> throw IllegalArgumentException("Unsupported dbType for URL building: $dbType")
        }
    }

    // Data class to hold database connection and metadata
    data class DatabaseConnectionInfo(
        val dataSource: DataSource,
        val dbHost: String?,
        val dbName: String,
        val dbUsername: String
    )

    private fun createDatabaseConnection(rule: RuleInfo): DatabaseConnectionInfo {
        // 连接信息（数据库连接信息）
        val dataSourceId = rule.dataSource
        val metadataSource = dataSourceId!!.toLongOrNull()?.let {
            metadataDataSourceRepository.findById(it)
        }?.getOrNull()
        if (metadataSource == null) {
            throw IllegalArgumentException("Metadata source not found for id $dataSourceId")
        }

        if (!(metadataSource.activeFlag)) {
            throw IllegalArgumentException("Metadata source is not active: $metadataSource")
        }

        val dbType = metadataSource.dbType
        val dbName = metadataSource.dbName
        val dbPort = metadataSource.dbPort
        val dbUrl = metadataSource.dbUrl

        // First try using customJdbcUrl, fall back to buildJdbcUrl if blank
        val jdbcUrl = if (!metadataSource.customJdbcUrl.isNullOrBlank()) {
            log.info("Using custom JDBC URL for datasource $dataSourceId: ${metadataSource.customJdbcUrl}")
            metadataSource.customJdbcUrl
        } else {
            val builtUrl = buildJdbcUrl(dbType, dbUrl!!, dbName, dbPort!!)
            log.info("Using built JDBC URL for datasource $dataSourceId: $builtUrl")
            builtUrl
        }

        val username = metadataSource.dbUsername
        val password = metadataSource.dbPassword

        val dataSource = createDataSource(dbType, jdbcUrl, username, password, rule.ruleName, dbName)

        return DatabaseConnectionInfo(
            dataSource = dataSource,
            dbHost = dbUrl,
            dbName = dbName,
            dbUsername = username
        )
    }

    /**
     * 处理规则 (Process Rule)
     * 根据规则类型选择相应的处理逻辑
     */
    private fun processRule(rule: RuleInfo, context: TaskMessage): Result<Int> {
        val runId = context.runId
        val ruleId = rule.ruleId
        val taskId = context.taskId

        log.info("Processing rule $ruleId (${rule.ruleName}) for run $runId")
        logExecutionStep(runId, ruleId, taskId, "INFO", "Starting processing for rule ${rule.ruleName}")

        // 确定规则类型并验证规则 (Determine rule type and validate rule)
        val ruleType = rule.determineRuleType()
        val validationResult = rule.validate()

        if (validationResult.isFailure) {
            val errorMessage = validationResult.exceptionOrNull()?.message ?: "规则验证失败 (Rule validation failed)"
            log.error("Rule validation failed for rule $ruleId: $errorMessage")
            logExecutionStep(runId, ruleId, taskId, "ERROR", "规则验证失败: $errorMessage")
            return Result.failure(validationResult.exceptionOrNull() ?: IllegalArgumentException(errorMessage))
        }

        logExecutionStep(
            runId,
            ruleId,
            taskId,
            "INFO",
            "规则类型: ${ruleType.name}, 验证通过 (Rule type: ${ruleType.name}, validation passed)"
        )

        // 根据规则类型选择处理方法 (Choose processing method based on rule type)
        return processRuleWithDatabase(rule, context, ruleType)
    }

    /**
     * 执行计数查询 (Execute Count Query)
     * 执行 SQL 查询并返回计数结果
     */
    private fun executeCountQuery(connection: DatabaseConnectionInfo, sql: String): Long {
        val queryExecutor = JdbcQueryExecutor(connection.dataSource)
        return queryExecutor.queryCount(sql)
    }

    /**
     * 创建数据库连接 (Create Database Connection)
     * 重载版本，支持源/目标数据库
     */
    private fun createDatabaseConnection(
        rule: RuleInfo,
        isSource: Boolean = false,
        isTarget: Boolean = false
    ): DatabaseConnectionInfo {
        val dataSourceId = when {
            isSource -> rule.sourceDataSource!!
            isTarget -> rule.targetDataSource!!
            else -> rule.dataSource!!
        }

        val datasourceIdInt = dataSourceId.toLong()

        val databaseName = when {
            isSource -> rule.sourceDatabaseName
            isTarget -> rule.targetDatabaseName
            else -> rule.databaseName
        }

        requireNotNull(databaseName) { "Database name is not set." }

        // 使用现有的连接创建逻辑
        val metadataSource = metadataDataSourceRepository
            .findById(datasourceIdInt)
            .orElseThrow { IllegalArgumentException("DataSource not found: $dataSourceId") }

        if (!(metadataSource.activeFlag)) {
            throw IllegalArgumentException("Metadata source is not active: $metadataSource")
        }

        val dbType = metadataSource.dbType
        val dbUrl = metadataSource.dbUrl
        val dbUsername = metadataSource.dbUsername
        val dbPassword = metadataSource.dbPassword
        val dbHost = metadataSource.dbUrl
        val dbPort = metadataSource.dbPort
        val dbName = metadataSource.dbName

        // First try using customJdbcUrl, fall back to buildJdbcUrl if blank
        val url = if (!metadataSource.customJdbcUrl.isNullOrBlank()) {
            log.info("Using custom JDBC URL for datasource $dataSourceId: ${metadataSource.customJdbcUrl}")
            metadataSource.customJdbcUrl
        } else {
            val builtUrl = buildJdbcUrl(dbType, dbUrl!!, databaseName ?: "", dbPort!!)
            log.info("Using built JDBC URL for datasource $dataSourceId: $builtUrl")
            builtUrl
        }

        val dataSource = createDataSource(dbType, url, dbUsername, dbPassword, rule.ruleName, dbName)

        return DatabaseConnectionInfo(
            dataSource = dataSource,
            dbHost = dbHost,
            dbName = databaseName,
            dbUsername = dbUsername
        )
    }

    /**
     * 处理规则并与数据库交互 (Process Rule with Database)
     * 通用的规则处理逻辑，用于处理不同类型的规则
     */
    private fun processRuleWithDatabase(rule: RuleInfo, context: TaskMessage, ruleType: RuleType): Result<Int> {
        val runId = context.runId
        val ruleId = rule.ruleId
        val taskId = context.taskId

        // 1. 获取问题实体 (Get issue entity)
        val issue = acquireIssueForRunning(ruleId, taskId)
        if (issue == null) {
            log.info("Issue for rule $ruleId not found or created by another node")
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Issue acquisition failed or already running on another node."
            )
            return Result.failure(RuntimeException("Issue for rule $ruleId not found or created by another node"))
        }
        logExecutionStep(runId, ruleId, taskId, "INFO", "Issue acquired successfully. Issue ID: ${issue.id}")

        // 确保问题ID存在 (Ensure issue id is present)
        val issueId = issue.id
        if (issueId == null) {
            log.error("f5c8a1b2 | Acquired issue has null ID for ruleId=$ruleId")
            logExecutionStep(runId, ruleId, taskId, "ERROR", "Acquired issue has null ID.")
            return Result.failure(RuntimeException("Acquired issue has null ID"))
        }
        logExecutionStep(runId, ruleId, taskId, "INFO", "Issue ID is valid: $issueId")

        // 2. 创建初始运行历史记录 (Create initial run history)
        val startTime = LocalDateTime.now()
        logExecutionStep(runId, ruleId, taskId, "INFO", "Creating initial run history entry.")

        val initialRunHistory = IssueRunHistoryEntity(
            issueId = issueId,
            runId = runId,
            startTime = startTime,
            status = RunStatus.STARTED,
            taskId = taskId,
            ruleId = ruleId,
        )

        var savedRunHistory: IssueRunHistoryEntity? = null
        try {
            savedRunHistory = issueRunHistoryRepository.save(initialRunHistory)
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Initial run history saved successfully. History ID: ${savedRunHistory.id}"
            )
        } catch (e: Exception) {
            log.error("624c4cd7 | Failed to save initial run history for issueId=$issueId, runId=$runId", e)
            logExecutionStep(runId, ruleId, taskId, "ERROR", "Failed to save initial run history: ${e.message}")
            // 回滚问题状态 (Revert issue status if history fails)
            issueRepository.save(issue.copy(currentStage = IssueStage.ERROR, updatedAt = LocalDateTime.now()))
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Issue status reverted to ERROR due to history save failure."
            )
            return Result.failure(RuntimeException("Failed to save initial run history"))
        }
        if (savedRunHistory == null) {
            log.info("be27ba2c | Aborting task for issueId=$issueId, runId=$runId")
            logExecutionStep(runId, ruleId, taskId, "INFO", "Aborting task due to initial run history save failure.")
            return Result.failure(RuntimeException("Failed to save initial run history"))
        }

        // 3. 根据规则类型创建数据库连接 (Create database connection based on rule type)
        var dbConnectionInfo: DatabaseConnectionInfo? = null
        var sourceConnection: DatabaseConnectionInfo? = null
        var targetConnection: DatabaseConnectionInfo? = null

        try {
            if (ruleType == RuleType.SQL_QUERY) {
                // Extract metadata information for logging before creating connection
                val dataSourceId = rule.dataSource
                val metadataSource = dataSourceId!!.toLongOrNull()?.let {
                    metadataDataSourceRepository.findById(it)
                }?.getOrNull()

                if (metadataSource != null) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "Attempting to create database connection to host: ${metadataSource.dbUrl}, database: ${metadataSource.dbName}, username: ${metadataSource.dbUsername}"
                    )
                } else {
                    logExecutionStep(runId, ruleId, taskId, "INFO", "Attempting to create database connection.")
                }

                dbConnectionInfo = createDatabaseConnection(rule)
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Database connection created successfully.",
                    dbHost = dbConnectionInfo.dbHost,
                    dbName = dbConnectionInfo.dbName,
                    dbUsername = dbConnectionInfo.dbUsername
                )
            } else if (ruleType == RuleType.COUNT_COMPARISON) {
                // Extract source metadata information for logging before creating connection
                val sourceDataSourceId = rule.sourceDataSource
                val sourceMetadataSource = sourceDataSourceId!!.toLongOrNull()?.let {
                    metadataDataSourceRepository.findById(it)
                }?.getOrNull()

                if (sourceMetadataSource != null) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "Creating source database connection to host: ${sourceMetadataSource.dbUrl}, database: ${rule.sourceDatabaseName}, username: ${sourceMetadataSource.dbUsername}"
                    )
                } else {
                    logExecutionStep(runId, ruleId, taskId, "INFO", "Creating source database connection.")
                }

                sourceConnection = createDatabaseConnection(rule, isSource = true)
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Source database connection created successfully.",
                    dbHost = sourceConnection.dbHost,
                    dbName = sourceConnection.dbName,
                    dbUsername = sourceConnection.dbUsername
                )

                // Extract target metadata information for logging before creating connection
                val targetDataSourceId = rule.targetDataSource
                val targetMetadataSource = targetDataSourceId!!.toLongOrNull()?.let {
                    metadataDataSourceRepository.findById(it)
                }?.getOrNull()

                if (targetMetadataSource != null) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "Creating target database connection to host: ${targetMetadataSource.dbUrl}, database: ${rule.targetDatabaseName}, username: ${targetMetadataSource.dbUsername}"
                    )
                } else {
                    logExecutionStep(runId, ruleId, taskId, "INFO", "Creating target database connection.")
                }

                targetConnection = createDatabaseConnection(rule, isTarget = true)
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Target database connection created successfully.",
                    dbHost = targetConnection.dbHost,
                    dbName = targetConnection.dbName,
                    dbUsername = targetConnection.dbUsername
                )
            }

            // 4. 执行查询并处理结果 (Execute queries and process results)
            var errorCount: Int = 0
            var totalCount: Long? = null

            if (ruleType == RuleType.SQL_QUERY && dbConnectionInfo != null) {
                val queryExecutor = JdbcQueryExecutor(dbConnectionInfo.dataSource)
                val cancelContext = CancelContext()

                val countSql = rule.countSql
                logExecutionStep(runId, ruleId, taskId, "INFO", "Executing count query: $countSql")
                totalCount = try {
                    if (countSql.isNullOrBlank()) {
                        null
                    } else {
                        queryExecutor.queryCount(countSql)
                    }
                } catch (e: Exception) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "ERROR",
                        "Count query failed: ${e.message}",
                        dbHost = dbConnectionInfo.dbHost,
                        dbName = dbConnectionInfo.dbName,
                        dbUsername = dbConnectionInfo.dbUsername,
                        sqlStatus = "ERROR"
                    )
                    throw e
                }
                log.info("c743f7b8 | Total count: $totalCount")
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Count query completed. Total count: $totalCount",
                    dbHost = dbConnectionInfo.dbHost,
                    dbName = dbConnectionInfo.dbName,
                    dbUsername = dbConnectionInfo.dbUsername,
                    sqlStatus = "SUCCESS"
                )

                val baseSql = rule.ruleSql ?: throw IllegalArgumentException("Rule SQL is not set.")
                log.info("22d8d1b3 | Base SQL: $baseSql")
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Starting paged execution for base SQL.",
                    dbHost = dbConnectionInfo.dbHost,
                    dbName = dbConnectionInfo.dbName,
                    dbUsername = dbConnectionInfo.dbUsername
                )

                errorCount = try {
                    val businessKeyList = rule.businessKeyList ?: emptyList()
                    DatabaseExecutor.executePagedAndSave(
                        dbType = "hive", // Note: This seems hardcoded to "hive". Consider making it dynamic based on metadataSource.dbType.
                        baseSql = baseSql,
                        ruleId = ruleId,
                        runId = runId,
                        cancelContext = cancelContext,
                        queryExecutor = queryExecutor,
                        resultBatchProcessor = resultBatchProcessor,
                        pageSize = 20000,
                        businessKeyList = businessKeyList
                    )
                } catch (e: Exception) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "ERROR",
                        "Paged execution failed: ${e.message}",
                        dbHost = dbConnectionInfo.dbHost,
                        dbName = dbConnectionInfo.dbName,
                        dbUsername = dbConnectionInfo.dbUsername,
                        sqlStatus = "ERROR"
                    )
                    throw e
                }
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Paged execution completed. Found $errorCount error rows.",
                    dbHost = dbConnectionInfo.dbHost,
                    dbName = dbConnectionInfo.dbName,
                    dbUsername = dbConnectionInfo.dbUsername,
                    sqlStatus = "SUCCESS"
                )
            } else if (ruleType == RuleType.COUNT_COMPARISON && sourceConnection != null && targetConnection != null) {
                // Execute count SQL query if available (similar to SQL_QUERY logic)
                val countSql = rule.countSql
                if (!countSql.isNullOrBlank()) {
                    logExecutionStep(runId, ruleId, taskId, "INFO", "Executing count query: $countSql")
                    totalCount = try {
                        // Use source connection for count query by default, could be configurable
                        executeCountQuery(sourceConnection, countSql)
                    } catch (e: Exception) {
                        logExecutionStep(
                            runId,
                            ruleId,
                            taskId,
                            "ERROR",
                            "Count query failed: ${e.message}",
                            dbHost = sourceConnection.dbHost,
                            dbName = sourceConnection.dbName,
                            sqlStatus = "ERROR"
                        )
                        throw e
                    }
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "Count query completed. Total count: $totalCount",
                        dbHost = sourceConnection.dbHost,
                        dbName = sourceConnection.dbName,
                        sqlStatus = "SUCCESS"
                    )
                }

                // 执行源和目标SQL查询 (Execute source and target SQL queries)
                val sourceSql = rule.sourceSql!!
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Executing source count query: $sourceSql",
                    dbHost = sourceConnection.dbHost,
                    dbName = sourceConnection.dbName
                )

                val sourceCount = try {
                    executeCountQuery(sourceConnection, sourceSql)
                } catch (e: Exception) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "ERROR",
                        "Source count query failed: ${e.message}",
                        dbHost = sourceConnection.dbHost,
                        dbName = sourceConnection.dbName,
                        sqlStatus = "ERROR"
                    )
                    throw e
                }
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Source count query completed. Count: $sourceCount",
                    dbHost = sourceConnection.dbHost,
                    dbName = sourceConnection.dbName,
                    sqlStatus = "SUCCESS"
                )

                val targetSql = rule.targetSql!!
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Executing target count query: $targetSql",
                    dbHost = targetConnection.dbHost,
                    dbName = targetConnection.dbName
                )

                val targetCount = try {
                    executeCountQuery(targetConnection, targetSql)
                } catch (e: Exception) {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "ERROR",
                        "Target count query failed: ${e.message}",
                        dbHost = targetConnection.dbHost,
                        dbName = targetConnection.dbName,
                        sqlStatus = "ERROR"
                    )
                    throw e
                }
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Target count query completed. Count: $targetCount",
                    dbHost = targetConnection.dbHost,
                    dbName = targetConnection.dbName,
                    sqlStatus = "SUCCESS"
                )

                // 根据比较类型和阈值进行比较 (Compare results based on comparison type and threshold)
                val comparisonType = rule.comparisonType!!
                val threshold = rule.threshold!!

                val comparisonResult = compareResults(sourceCount, targetCount, comparisonType, threshold)
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Comparison performed. Source: $sourceCount, Target: $targetCount, Type: $comparisonType, Threshold: $threshold, Result: ${if (comparisonResult) "Within threshold" else "Exceeds threshold"}"
                )

                // 根据比较结果决定是否需要创建错误记录 (Create error records based on comparison result)
                errorCount = if (!comparisonResult) {
                    // 创建一条错误记录 (创建具体的错误记录逻辑这里可能需要自定义)
                    val comparisonMessage = when (comparisonType) {
                        ComparisonType.ABSOLUTE_DIFFERENCE -> {
                            val diff = abs(targetCount - sourceCount)
                            "absolue difference: $diff, threshold: $threshold"
                        }

                        ComparisonType.TARGET_DIV_SOURCE_RATIO -> {
                            val ratio =
                                if (sourceCount != 0L) targetCount.toDouble() / sourceCount.toDouble() else Double.POSITIVE_INFINITY
                            "target/source ratio: $ratio, threshold: $threshold"
                        }

                        ComparisonType.SOURCE_DIV_TARGET_RATIO -> {
                            val ratio =
                                if (targetCount != 0L) sourceCount.toDouble() / targetCount.toDouble() else Double.POSITIVE_INFINITY
                            "source/target ratio: $ratio, threshold: $threshold"
                        }
                    }
                    1 // 表示找到一个问题
                } else {
                    0 // 表示没有问题
                }
            }

            // 5. 标记之前的结果为已修复 (Mark previous results as fixed)
            if (ruleType == RuleType.SQL_QUERY && dbConnectionInfo != null) {
                logExecutionStep(
                    runId,
                    ruleId,
                    taskId,
                    "INFO",
                    "Marking previous results as fixed for rule $ruleId, excluding run $runId.",
                    dbHost = dbConnectionInfo.dbHost,
                    dbName = dbConnectionInfo.dbName,
                    dbUsername = dbConnectionInfo.dbUsername,
                    sqlStatus = "SUCCESS"
                )
                val previousRows = ruleResultRepository.findByRuleIdAndRunIdIsNotAndIsFixed(ruleId, runId, 0)
                log.info("6adfa8ca | previousRows size: ${previousRows.size}")
                if (previousRows.isNotEmpty()) {
                    ruleResultRepository.saveAll(previousRows.map {
                        it.copy(
                            isFixed = 1,
                            updatedAt = LocalDateTime.now()
                        )
                    })
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "Marked ${previousRows.size} previous results as fixed.",
                        dbHost = dbConnectionInfo.dbHost,
                        dbName = dbConnectionInfo.dbName,
                        dbUsername = dbConnectionInfo.dbUsername,
                        sqlStatus = "SUCCESS"
                    )
                } else {
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "INFO",
                        "No previous results found to mark as fixed.",
                        dbHost = dbConnectionInfo.dbHost,
                        dbName = dbConnectionInfo.dbName,
                        dbUsername = dbConnectionInfo.dbUsername,
                        sqlStatus = "SUCCESS"
                    )
                }
            } else if (ruleType == RuleType.COUNT_COMPARISON) {
                val findByRuleIdAndTaskId = ruleResultRepository.findByRuleId(ruleId)
                val now = LocalDateTime.now()
                if (errorCount == 0) {
                    if (findByRuleIdAndTaskId.isNotEmpty()) {
                        logExecutionStep(
                            runId,
                            ruleId,
                            taskId,
                            "INFO",
                            "Marking previous results as fixed for rule $ruleId"
                        )
                        findByRuleIdAndTaskId.forEach {
                            ruleResultRepository.save(
                                it.copy(isFixed = 1, updatedAt = now)
                            )
                        }
                    }
                } else {
                    // 创建结果记录
                    val resultEntity = RuleResultEntity(
                        runId = runId,
                        ruleId = ruleId,
                        dbType = "hive",
                        originalSql = listOf(rule.sourceSql, rule.targetSql).toString(),
                        executedAt = LocalDateTime.now(),
                        rowData = null,
                        businessKeyData = null,
                        businessKeyDataMd5 = null,
                        rowIndex = null,
                        executionTimeMs = 0L,
                        isFixed = 0
                    )
                    ruleResultRepository.save(resultEntity)
                    logExecutionStep(
                        runId,
                        ruleId,
                        taskId,
                        "WARNING",
                        "Created error record for count comparison: $errorCount"
                    )
                }
            }

            // 6. 更新问题状态 (Update Issue status)
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Updating Issue status to COMPLETED."
            )
            issueRepository.save(
                issue.copy(
                    currentStage = IssueStage.COMPLETED,
                    updatedAt = LocalDateTime.now()
                )
            )
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Issue status updated to COMPLETED."
            )

            // 7. 更新历史记录状态 (Update History status)
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Updating Run History status to COMPLETED."
            )
            issueRunHistoryRepository.save(
                savedRunHistory.copy(
                    endTime = LocalDateTime.now(),
                    errorRowCount = errorCount,
                    // totalScanCount = if (ruleType == RuleType.SQL_QUERY) totalCount else null,
                    totalScanCount = totalCount,
                    status = RunStatus.COMPLETED
                )
            )
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Run History status updated to COMPLETED."
            )

            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Rule execution completed successfully."
            )
            return Result.success(errorCount)

        } catch (e: Exception) {
            log.error("Error executing rule $ruleId: ${e.message}", e)
            // We might not have connection info if the error occurred before creating the connections
            val dbHost = dbConnectionInfo?.dbHost ?: sourceConnection?.dbHost
            val dbName = dbConnectionInfo?.dbName ?: sourceConnection?.dbName
            val dbUsername = dbConnectionInfo?.dbUsername ?: sourceConnection?.dbUsername

            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "ERROR",
                "Error during rule execution: ${e.message}",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )

            // Update Issue status
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Updating Issue status to ERROR.",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )
            issueRepository.save(
                issue.copy(
                    currentStage = IssueStage.ERROR,
                    updatedAt = LocalDateTime.now()
                )
            )
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Issue status updated to ERROR.",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )

            // Update History status
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Updating Run History status to FAILED.",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )
            issueRunHistoryRepository.save(
                savedRunHistory.copy(
                    endTime = LocalDateTime.now(),
                    status = RunStatus.FAILED,
                    errorMessage = e.message ?: "Unknown error during rule execution"
                )
            )
            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "INFO",
                "Run History status updated to FAILED.",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )

            logExecutionStep(
                runId,
                ruleId,
                taskId,
                "ERROR",
                "Rule execution failed.",
                dbHost = dbHost,
                dbName = dbName,
                dbUsername = dbUsername,
                sqlStatus = "ERROR"
            )
            return Result.failure(e)
        }
    }

    private fun processPostTask(task: TaskDetail, runId: String) {
        log.info("e8bd0e09 | Processing post task ${task.taskId} (${task.taskName})")

        for (rule in task.rules) {
            try {
                log.info("7199c89d | Processing post task rule ${rule.ruleId} (${rule.ruleName})")
                processRule(
                    rule, TaskMessage(
                        taskId = task.taskId,
                        taskName = task.taskName,
                        rules = listOf(rule),
                        postTasks = emptyList(),
                        runId = runId,
                        operator = "SYSTEM"
                    )
                )
                log.info("14b4ed3e | Post task rule ${rule.ruleId} processed successfully")
            } catch (e: Exception) {
                log.error("Error processing post task rule ${rule.ruleId}: ${e.message}", e)
                // 继续处理其他规则
            }
        }
    }

    companion object {
        const val TOPIC = "data-quality-tasks"
    }

    /**
     * 比较结果 (Compare Results)
     * 根据比较类型和阈值比较源和目标计数
     */
    private fun compareResults(
        sourceCount: Long,
        targetCount: Long,
        comparisonType: ComparisonType,
        threshold: Double
    ): Boolean {
        return when (comparisonType) {
            ComparisonType.ABSOLUTE_DIFFERENCE -> {
                val diff = abs(targetCount - sourceCount)
                diff <= threshold
            }

            ComparisonType.TARGET_DIV_SOURCE_RATIO -> {
                val ratio =
                    if (sourceCount != 0L) targetCount.toDouble() / sourceCount.toDouble() else Double.POSITIVE_INFINITY
                ratio <= threshold
            }

            ComparisonType.SOURCE_DIV_TARGET_RATIO -> {
                val ratio =
                    if (targetCount != 0L) sourceCount.toDouble() / targetCount.toDouble() else Double.POSITIVE_INFINITY
                ratio <= threshold
            }
        }
    }

    /**
     * Track acquired issues for cleanup during shutdown
     */
    private fun trackAcquiredIssue(ruleId: Long, taskId: Long, issueId: Long) {
        val processingKey = activeProcessingTasks.keys.find { key ->
            val parts = key.split("-")
            parts.size >= 2 && parts[0] == taskId.toString()
        }

        processingKey?.let { key ->
            activeProcessingTasks[key]?.acquiredIssues?.add(issueId)
        }
    }

    /**
     * Clean up acquired issues during shutdown
     */
    private fun cleanupAcquiredIssues(context: TaskProcessingContext) {
        try {
            log.info("Cleaning up ${context.acquiredIssues.size} acquired issues for taskId=${context.taskId}, runId=${context.runId}")

            context.acquiredIssues.forEach { issueId ->
                try {
                    val issue = issueRepository.findById(issueId).orElse(null)
                    if (issue != null && issue.currentStage == IssueStage.RUNNING) {
                        // Mark as CRASHED for retry by other nodes
                        val crashedIssue = issue.copy(
                            currentStage = IssueStage.CRASHED,
                            updateBy = "SYSTEM_SHUTDOWN_${hostInfo.hostIdentifier}",
                            updatedAt = LocalDateTime.now()
                        )
                        issueRepository.save(crashedIssue)
                        log.info("Marked issue as CRASHED during shutdown: issueId=$issueId, ruleId=${issue.ruleId}, taskId=${issue.taskId}")
                    }
                } catch (e: Exception) {
                    log.error("Failed to cleanup issue during shutdown: issueId=$issueId", e)
                }
            }
        } catch (e: Exception) {
            log.error("Error during issue cleanup for taskId=${context.taskId}, runId=${context.runId}", e)
        }
    }

    /**
     * Clean up all active processing tasks during shutdown
     */
    private fun cleanupAllActiveProcessingTasks() {
        if (activeProcessingTasks.isEmpty()) {
            log.info("No active processing tasks to cleanup")
            return
        }

        log.info("Cleaning up ${activeProcessingTasks.size} active processing tasks during shutdown")

        activeProcessingTasks.values.forEach { context ->
            cleanupAcquiredIssues(context)
        }

        activeProcessingTasks.clear()
    }

    // Shutdown hook to clean up resources
    @PreDestroy
    fun shutdown() {
        log.info("Starting graceful shutdown of TaskConsumer on host: ${hostInfo.hostIdentifier}")

        // Set shutdown flag to prevent new message processing
        isShuttingDown.set(true)

        // Give some time for current processing to complete
        try {
            Thread.sleep(2000) // Wait 2 seconds for current processing to wrap up
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt()
        }

        // Cancel all active timeout tasks (取消所有活动的超时任务)
        activeProcessingTasks.values.forEach { context ->
            context.timeoutFuture?.cancel(true)
        }

        // Clean up any remaining active processing tasks
        cleanupAllActiveProcessingTasks()

        // Shutdown scheduler
        scheduler.shutdown()
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("Scheduler did not terminate within 5 seconds, forcing shutdown")
                scheduler.shutdownNow()
            }
        } catch (e: InterruptedException) {
            log.warn("Interrupted while waiting for scheduler termination")
            scheduler.shutdownNow()
            Thread.currentThread().interrupt()
        }

        // Shutdown message timeout executor (关闭消息超时执行器)
        messageTimeoutExecutor.shutdown()
        try {
            if (!messageTimeoutExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("Message timeout executor did not terminate within 5 seconds, forcing shutdown")
                messageTimeoutExecutor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            log.warn("Interrupted while waiting for message timeout executor termination")
            messageTimeoutExecutor.shutdownNow()
            Thread.currentThread().interrupt()
        }

        log.info("TaskConsumer shutdown completed on host: ${hostInfo.hostIdentifier}")
    }
}
