package com.datayes.domain

/**
 * 规则类型判断和验证相关的扩展函数
 * 采用函数式编程风格，将数据结构与其操作分离
 */

/**
 * 确定规则类型 (Determine rule type)
 * 基于规则属性判断是SQL查询规则还是计数比较规则
 */
fun RuleInfo.determineRuleType(): RuleType {
    return when {
        // 当存在计数比较规则必要属性时判定为计数比较规则 (Count Comparison Rule)
        sourceSql != null && targetSql != null &&
                sourceDataSource != null && targetDataSource != null &&
                comparisonType != null && threshold != null -> RuleType.COUNT_COMPARISON

        // 否则判定为 SQL 查询规则 (SQL Query Rule)
        else -> RuleType.SQL_QUERY
    }
}

/**
 * 验证规则 (Validate rule)
 * 根据规则类型验证必要属性
 */
fun RuleInfo.validate(): Result<Boolean> {
    return when (determineRuleType()) {
        RuleType.SQL_QUERY -> validateSqlQueryRule()
        RuleType.COUNT_COMPARISON -> validateCountComparisonRule()
    }
}

/**
 * 验证 SQL 查询规则 (Validate SQL Query Rule)
 * 确保所有必需的属性都存在且有效
 */
private fun RuleInfo.validateSqlQueryRule(): Result<Boolean> {
    if (dataSource.isNullOrBlank() ||
        ruleSql.isNullOrBlank() ||
        countSql.isNullOrBlank()
    // || businessKeyList.isNullOrEmpty()
    ) {
        return Result.failure(IllegalArgumentException("SQL Query Rule 缺少必要属性: 必须提供dataSource, ruleSql, countSql和businessKeyList"))
    }
    return Result.success(true)
}

/**
 * 验证计数比较规则 (Validate Count Comparison Rule)
 * 确保所有必需的属性都存在且有效
 */
private fun RuleInfo.validateCountComparisonRule(): Result<Boolean> {
    if (sourceSql.isNullOrBlank() ||
        targetSql.isNullOrBlank() ||
        sourceDataSource.isNullOrBlank() ||
        targetDataSource.isNullOrBlank() ||
        comparisonType == null ||
        threshold == null
    ) {
        return Result.failure(IllegalArgumentException("Count Comparison Rule 缺少必要属性: 必须提供sourceSql, targetSql, sourceDataSource, targetDataSource, comparisonType和threshold"))
    }
    return Result.success(true)
}

/**
 * 比较源和目标结果 (Compare source and target results)
 * 根据比较类型和阈值判断结果是否符合预期
 */
fun compareResults(
    sourceCount: Long,
    targetCount: Long,
    comparisonType: ComparisonType,
    threshold: Double
): Boolean {
    return when (comparisonType) {
        ComparisonType.ABSOLUTE_DIFFERENCE -> {
            val difference = Math.abs(targetCount - sourceCount)
            difference <= threshold
        }

        ComparisonType.TARGET_DIV_SOURCE_RATIO -> {
            if (sourceCount == 0L) return false
            val ratio = targetCount.toDouble() / sourceCount.toDouble()
            Math.abs(1 - ratio) <= threshold
        }

        ComparisonType.SOURCE_DIV_TARGET_RATIO -> {
            if (targetCount == 0L) return false
            val ratio = sourceCount.toDouble() / targetCount.toDouble()
            Math.abs(1 - ratio) <= threshold
        }
    }
}
