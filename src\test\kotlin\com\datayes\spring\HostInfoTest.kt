package com.datayes.spring

import org.springframework.boot.test.context.SpringBootTest
import strikt.api.expectThat
import strikt.assertions.isNotBlank
import javax.inject.Inject
import kotlin.test.Test

@SpringBootTest
class HostInfoTest {

    @Inject
    lateinit var hostInfo: HostInfo

    // test method getHostIp return not null not blank
    @Test
    fun `should return not null not blank`() {

        // when
        val hostIp = hostInfo.getHostIp()

        // expect
        expectThat(hostIp).isNotBlank()
    }
}