package com.datayes.spring.kafka

import com.datayes.domain.CancelMessage
import com.datayes.executor.DatabaseExecutor
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
class CancelConsumer {

    private val log = LoggerFactory.getLogger(CancelConsumer::class.java)

    @KafkaListener(
        topics = ["data-quality-cancel"],
        groupId = "executor-group-cancel-#{@hostInfo.getHostIp()}",
        containerFactory = "cancelKafkaListenerContainerFactory"
    )
    fun onCancelMessage(message: CancelMessage, acknowledgment: Acknowledgment) {
        try {
            // 使用规则ID调用DatabaseExecutor.cancelRun方法取消任务执行
            log.info("66966ef6 | 收到取消任务消息 (Received cancel message): ruleId=${message.ruleId}")
            val canceled = DatabaseExecutor.cancelRun(message.ruleId)
            if (canceled) {
                log.info("0f3f6395 | 已取消规则执行 (Rule execution canceled): ruleId=${message.ruleId}")
            } else {
                log.info("No active execution found to cancel for ruleId=${message.ruleId}")
            }
            
            // 无论是否找到可取消的执行，都确认消息 (Acknowledge message regardless of whether execution was found)
            acknowledgment.acknowledge()
            log.debug("Cancel message acknowledged for ruleId=${message.ruleId}")
        } catch (e: Exception) {
            log.error("Error processing cancel message for ruleId=${message.ruleId}", e)
            // 发生异常时不确认消息，让其重新处理 (Don't acknowledge on error, let it be reprocessed)
        }
    }

    companion object {
        const val TOPIC = "data-quality-cancel"
    }
}