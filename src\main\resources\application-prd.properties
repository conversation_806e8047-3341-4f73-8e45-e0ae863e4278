spring.datasource.driver-class-name    = com.mysql.cj.jdbc.Driver
spring.datasource.url                  = ********************************************************************************************************************
spring.datasource.username             = dbausr
spring.datasource.password             = J^DqUUzjbT$cMmYq

spring.kafka.bootstrap-servers         = 10.5.134.96:9092
spring.kafka.listener.auto-startup     = true

app.message.processing.timeout.minutes = 45