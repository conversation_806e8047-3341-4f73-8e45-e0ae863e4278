package com.datayes.spring

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

class ObjectMapperBeanTest {

    @Test
    fun `should support kotlin data class`() {
        // given
        val objectMapper = JacksonConfig().objectMapper()

        val json = """
            {
                "name": "<PERSON>",
                "age": 30
            }
        """.trimIndent()

        data class Person(val name: String, val age: Int)

        // when
        val person = objectMapper.readValue(json, Person::class.java)

        // then
        assertEquals("<PERSON>", person.name)
        assertEquals(30, person.age)
    }


    @Test
    fun `should support LocalDateTime`() {
        // given
        val objectMapper = JacksonConfig().objectMapper()

        val json = """
            {
                "name": "Meeting",
                "startTime": "2024-03-20T14:30:00"
            }
        """.trimIndent()

        data class Event(val name: String, val startTime: LocalDateTime)

        // when
        val event = objectMapper.readValue(json, Event::class.java)

        // then
        assertEquals("Meeting", event.name)
        assertEquals(LocalDateTime.of(2024, 3, 20, 14, 30, 0), event.startTime)
    }
}