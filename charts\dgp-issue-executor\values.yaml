# orion-registry 服务的默认值
# 定义的变量将用于 template 下的模板文件

# Pod 副本数量
replicaCount: 1

image:
  repository: registry.minshenglife.com/mslife-department-2/dgp-issue-executor
  # image 更新策略 Always、IfNotPresent
  pullPolicy: Always

# 应用暴露的端口
config:
  port: 9501


env:
  open:
    # 本实例服务端口
    SERVER_PORT: 9501
    MANAGEMENT_SERVER_PORT: 9501
    JAVA_TOOL_OPTIONS: -Xmx1536m -Dspring.profiles.active=qa

# Hive集群主机名映射配置
hostAliases:
  - ip: ***********
    hostnames:
      - tbds-10-9-112-25
  - ip: ***********
    hostnames:
      - tbds-10-9-112-26
  - ip: ***********
    hostnames:
      - tbds-10-9-112-27
  - ip: ***********
    hostnames:
      - tbds-10-9-112-28
  - ip: ***********
    hostnames:
      - tbds-10-9-112-29
  - ip: ***********
    hostnames:
      - tbds-10-9-112-30
  - ip: ***********
    hostnames:
      - tbds-10-9-112-31
  - ip: ***********
    hostnames:
      - tbds-10-9-112-32


# 资源限制
resources:
  # 最大限制
  limits:
    # cpu: 100m
    memory: 1536Mi
  # 请求值
  requests:
    # cpu: 100m
    memory: 1536Mi

## Liveness 和 Readiness 探针相关配置
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
livenessProbe:
  initialDelaySeconds: 180
  periodSeconds: 30
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3
readinessProbe:
  initialDelaySeconds: 5
  periodSeconds: 15
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3

service:
  # 是否启用 service 暴露本服务
  enabled: false
  # Service 名称
  name: dgp-issue-executor
  # Service 类型 ClusterIP、NodePort、LoadBalancer
  type: ClusterIP
  # 指定 service 的 clusterIP
  clusterIP:
  # 端口设置
  ports:
    # 服务端口
    http:
      port: 9501
      nodePort: 9501
    # 管理端口
    actuator:
      port: 9501
      nodePort: 9501
  ## externalIPs 设置
  # externalIPs:
  #   - externalIp1
  annotations: { }
