package com.datayes.domain

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

@Table("rule_execution_results")
data class RuleResultEntity(

    @Id
    val id: Long? = null,

    @Column("RULE_ID")
    val ruleId: Long,

    @Column("DB_TYPE")
    val dbType: String,

    @Column("ORIGINAL_SQL")
    val originalSql: String,

    @Column("RUN_ID")
    val runId: String,

    @Column("EXECUTED_AT")
    val executedAt: LocalDateTime,

    @Column("ROW_DATA")
    val rowData: String?,

    @Column("BUSINESS_KEY_DATA")
    val businessKeyData: String?,

    @Column("BUSINESS_KEY_DATA_MD5")
    val businessKeyDataMd5: String?,

    @Column("ROW_INDEX")
    val rowIndex: Int?,

    @Column("EXECUTION_TIME_MS")
    val executionTimeMs: Long,

    // 是否修复； 0: 未修复; 1: 已修复
    @Column("IS_FIXED")
    val isFixed: Int,

    @Column("CREATED_BY")
    val createdBy: String? = null,

    @Column("CREATED_AT")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("UPDATED_BY")
    val updatedBy: String? = null,

    @Column("UPDATED_AT")
    val updatedAt: LocalDateTime? = null,
)
