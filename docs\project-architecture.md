# 项目架构与设计（dgp-issue-executor）

> 本文档用于向业务方快速介绍本项目的整体实现方案，重点呈现架构思想、关键模块及其协作方式，便于后续的沟通、评审与扩展。

---

## 1. 概述（Overview）
本服务作为数据质量平台（DGP）的规则执行节点，接收任务消息，连接多种数据源，执行 SQL 规则并将结果写入中心库。整体采用 **事件驱动（Event-Driven）** + **分层架构（Layered Architecture）** 设计，核心语言为 **Kotlin**，运行框架为 **Spring Boot**。

## 2. 总体架构（Architecture）
```
┌───────────────────┐        ┌───────────────────┐
│    Kafka Topic    │        │     REST 调用     │
│  dgp.issue.task   │        │  /api/task/* ...  │
└─────────┬─────────┘        └─────────┬─────────┘
          │                             │
      (Kafka)                         (HTTP)
          │                             │
          ▼                             ▼
┌──────────────────────────────────────────────────────┐
│                  应用层（Application）               │
│  TaskConsumer / CancelConsumer / TaskController      │
└─────────┬──────────────────────────────┬────────────┘
          │                              │
          ▼                              ▼
┌──────────────────┐      ┌───────────────────────────┐
│  规则执行引擎     │      │      持久化适配器          │
│ (DatabaseExecutor)│      │Spring Data JDBC Repos†    │
└─────────┬────────┘      └───────────┬──────────────┘
          │                            │
          ▼                            ▼
   多源数据库（MySQL/Hive/…）     中心库（MySQL）
```
† `IssueRepository`, `RuleResultRepository`, `RuleExecutionLogRepository`, etc.

## 3. 分层说明（Layer Description）
1. **接口层（Interface Layer）**  
   * 对外暴露 **REST** 接口（`TaskController`, `HeartbeatController`）
   * 对内监听 **Kafka** 主题（`TaskConsumer`, `CancelConsumer`）

2. **应用层（Application Layer）**  
   * 解析任务消息，选择数据源并组装执行上下文  
   * 负责流程编排、超时控制与取消处理

3. **领域层（Domain Layer）**  
   * 纯粹的数据模型，使用 Kotlin `data class` 定义：`IssueEntity`, `IssueRunHistoryEntity`, `RuleResultEntity`…  
   * 枚举 `IssueStage`, `RunStatus` 映射业务状态机

4. **基础设施层（Infrastructure Layer）**  
   * 基于 **Spring Data JDBC** 的仓储实现；无需 `EntityManager`、更贴近 SQL  
   * `DatabaseExecutor` + `JdbcQueryExecutor` 封装跨数据库执行逻辑，支持 (MySQL)、(PostgreSQL)、(Oracle)、(Hive)  
   * 连接池采用 (HikariCP)，按任务动态构造

## 4. 关键实现细节（Key Details）
### 4.1 任务消费与调度（Task Consumption）
* `TaskConsumer` 使用 `@KafkaListener` 高并发消费 `dgp.issue.task` 主题。
* 消息体反序列化后映射到 `TaskMessage`，并立即创建 **取消标记（CancelContext）** 以便后续终止。
* 若收到相同 `ruleId` 的取消消息，`CancelConsumer` 会快速置位相关 `AtomicBoolean`，实现 **幂等取消（Idempotent Cancel）**。

### 4.2 规则执行引擎（Execution Engine）
* 核心入口 `DatabaseExecutor.executePagedAndSave`：  
  1. 为目标数据库构建 (HikariCP) DataSource。  
  2. 使用方言感知的 `buildPagedSql` 生成分页 SQL。  
  3. 逐页拉取数据，结合 `ResultBatchProcessor` 批量落表。  
  4. 过程中不断检查 `CancelContext`，实现秒级终止。
* 结果行在写入前会计算 `MD5`（`md5Hex`）生成去重 Key，保持数据一致性。

### 4.3 分页与结果批处理（Paging & Batch）
* 默认 `pageSize=10 000`，可在消息体中覆盖。
* `JdbcQueryExecutor` 针对 (Hive) 多列名冲突进行别名化处理，确保列唯一。
* `ResultBatchProcessor` 在 500 行批量写库，可配置；利用单连接事务提升吞吐。

### 4.4 运行日志与监控（Logging & Metrics）
* `RuleExecutionLogEntity` 记录每次分页的耗时、错误信息。  
* `IssueRunHistoryEntity` 记录一次完整执行的 **开始/结束/统计指标**。  
* Prometheus 兼容指标通过 `@Timed`（留扩展点）暴露。

### 4.5 取消机制（Cancellation）
* `cancelContextMap<ruleId, CancelContext>` 保存全局取消标记。  
* `DatabaseExecutor` 在每页循环及 JDBC 查询前检查 `cancelFlag`。  
* 保证 **至少一次检查 / 页面**，延迟 ≤1 s。

## 5. 数据模型（Data Model）
| 表名 | 说明 | 主键 | 关键字段 |
| ---- | ---- | ---- | -------- |
| `data_quality_issues` | 规则实例/问题 | `id` | `rule_id`, `status`, `current_stage` |
| `issue_run_history` | 每次执行记录 | `id` | `run_id`, `total_scan_count`, `error_row_count` |
| `rule_result` | 结果明细 | 复合 | `business_key`, `error_desc`, `run_id` |
| `rule_execution_log` | 分页日志 | `id` | `page_no`, `page_cost_ms`, `row_count` |

## 6. 异常处理与可靠性（Error Handling）
* 异常分类：网络 / SQL / 数据源配置 / 业务。对应更新 `RunStatus` 为 `FAILED` 并写 `errorMessage`。
* `@Retryable`（准备中）用于短暂网络波动自动重试。
* 通过 `Spring-Kafka` 开启 `enable-auto-commit=false` 与 **手动 ack**，确保 **消息至少一次** 处理。

## 7. 扩展与可维护性（Extensibility）
1. **新增数据源**：实现 `QueryExecutor` 子类即可；连接参数透过任务消息下发。  
2. **规则类型扩展**：在 `TaskMessage` 中新增 `ruleType`，于消费侧策略路由。  
3. **横向扩展**：无状态设计，部署多个实例即可按 (Kafka) 分区扩容。

## 8. 技术栈（Tech Stack）
* **Kotlin 1.9** + **Spring Boot 3.4**
* **Spring Data JDBC** 作为 ORM（轻量级、无懒加载）
* **Spring Kafka** 监听消息
* **HikariCP** 连接池
* **Jackson 2** Kotlin Module JSON 序列化
* **JUnit 5 / Strikt** 单元测试

## 9. 部署与运行（Deployment）
1. 运行 `mvn clean package` 产出可执行 **fat-jar**。  
2. 通过 `java -jar dgp-issue-executor.jar \`  
   `--spring.profiles.active=prod \`  
   `--spring.kafka.bootstrap-servers=…`  等参数启动。
3. 云原生环境推荐使用 (Helm) chart（见 `charts/` 目录）。

---

✅ **结语**  
该服务以 *(data-oriented)* + *(functional-core)* 思路构建，核心逻辑皆以纯函数 + 不可变数据实现，底层资源交给 (Spring Boot) 容器管理，既保证了可测试性，又易于在生产环境横向伸缩。
