package com.datayes.executor

import com.datayes.domain.RuleResultEntity
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.concurrent.atomic.AtomicBoolean

class ExecutePagedAndSaveTest {

    @Test
    fun `executePagedAndSave mysql should process all pages and pass correct entities`() {
        val received = mutableListOf<RuleResultEntity>()

        val stubQueryExecutor = object : QueryExecutor {
            var callCount = 0
            override fun queryPage(pagedSql: String): List<Map<String, Any?>> {
                return when (callCount++) {
                    0 -> List(2) { idx -> mapOf("id" to idx + 1, "name" to "User${idx + 1}") }
                    1 -> List(1) { mapOf("id" to 3, "name" to "User3") }
                    else -> emptyList()
                }
            }

            override fun queryCount(countSql: String): Long {
                TODO("Not yet implemented")
            }
        }

        val stubBatchProcessor = ResultBatchProcessor { batch ->
            received.addAll(batch)
        }

        val cancelContext = CancelContext(AtomicBoolean(false))

        val result = DatabaseExecutor.executePagedAndSave(
            dbType = "mysql",
            baseSql = "SELECT * FROM users",
            ruleId = 42L,
            runId = "123",
            cancelContext = cancelContext,
            queryExecutor = stubQueryExecutor,
            resultBatchProcessor = stubBatchProcessor,
            pageSize = 2,
            businessKeyList = emptyList(),
        )

        assertEquals(3, received.size)
        assertEquals(3, result)
    }
}
