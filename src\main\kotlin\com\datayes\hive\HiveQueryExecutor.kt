package com.datayes.hive

import java.sql.*
import org.slf4j.LoggerFactory

/**
 * An object providing utilities for executing SQL queries against a Hive database.
 */
object HiveQueryExecutor {

    private val logger = LoggerFactory.getLogger(HiveQueryExecutor::class.java)

    /**
     * Executes a SQL query against a Hive database and returns the results as a list of maps.
     * Each map represents a row, with column names as keys and column values as values.
     *
     * @param jdbcUrl The JDBC connection URL for the Hive database.
     * @param user The username for the Hive connection.
     * @param password The password for the Hive connection.
     * @param sql The SQL query to execute.
     * @return A list of maps, where each map is a row from the query result.
     * Returns an empty list if the query returns no results or an error occurs.
     */
    fun executeHiveQuery(jdbcUrl: String, user: String, password: String, sql: String): List<Map<String, Any?>> {
        // Ensure the Hive JDBC driver is loaded
        try {
            Class.forName("org.apache.hive.jdbc.HiveDriver")
        } catch (e: ClassNotFoundException) {
            // Handle the case where the driver is not found
            logger.error("Hive JDBC driver not found. Please ensure it's in your classpath.")
            return emptyList() // Return empty list on driver error
        }

        val results = mutableListOf<Map<String, Any?>>()

        // Use 'use' blocks for automatic resource management (Connection, Statement, ResultSet)
        try {
            // Use the passed-in parameters for the connection
            DriverManager.getConnection(jdbcUrl, user, password).use { conn ->
                conn.createStatement().use { stmt ->
                    stmt.executeQuery(sql).use { rs ->
                        val metaData = rs.metaData
                        val columnCount = metaData.columnCount

                        // Iterate through the result set row by row
                        while (rs.next()) {
                            val rowData = mutableMapOf<String, Any?>()
                            // Iterate through columns for the current row
                            for (i in 1..columnCount) {
                                val columnName = metaData.getColumnName(i)
                                // Use getObject to retrieve values, allowing for different types and nulls
                                val columnValue = rs.getObject(i)
                                rowData[columnName] = columnValue
                            }
                            results.add(rowData) // Add the row map to the results list
                        }
                    }
                }
            }
        } catch (e: SQLException) {
            // Handle SQL exceptions (e.g., connection errors, query errors)
            logger.error("An SQL error occurred:")
            return emptyList() // Return empty list on SQL error
        } catch (e: Exception) {
            // Handle any other unexpected exceptions
            logger.error("An unexpected error occurred:")
            return emptyList() // Return empty list on other errors
        }

        return results // Return the list of results
    }
}
