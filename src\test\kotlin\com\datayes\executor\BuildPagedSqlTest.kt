package com.datayes.executor

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class BuildPagedSqlTest {

    @Test
    fun `buildPagedSql generates correct mysql paging sql`() {
        val baseSql = "SELECT * FROM users WHERE status = 'active'"
        val offset = 40000
        val pageSize = 20000

        val expected = "SELECT * FROM users WHERE status = 'active' LIMIT 20000 OFFSET 40000"

        val actual = DatabaseExecutor.buildPagedSql("mysql", baseSql, offset, pageSize)

        assertEquals(expected, actual)
    }

    // test for sql that already has limit but no offset
    @Test
    fun `buildPagedSql generates correct mysql paging sql with limit and no offset`() {
        val baseSql = "SELECT * FROM users WHERE status = 'active' LIMIT 20000"
        val offset = 0
        val pageSize = 20000

        val expected = "SELECT * FROM users WHERE status = 'active' LIMIT 20000"

        val actual = DatabaseExecutor.buildPagedSql("mysql", baseSql, offset, pageSize)

        assertEquals(expected, actual)
    }

    @Test
    fun `buildPagedSql handles sql ending with semicolon correctly`() {
        val baseSql = "SELECT * FROM users WHERE status = 'active';"
        val offset = 40000
        val pageSize = 20000

        val expected = "SELECT * FROM users WHERE status = 'active' LIMIT 20000 OFFSET 40000"

        val actual = DatabaseExecutor.buildPagedSql("mysql", baseSql, offset, pageSize)

        assertEquals(expected, actual)
    }

    @Test
    fun `buildPagedSql handles sql with LIMIT and semicolon correctly when offset is 0`() {
        val baseSql = "SELECT * FROM users WHERE status = 'active' LIMIT 20000;"
        val offset = 0
        val pageSize = 20000

        val expected = "SELECT * FROM users WHERE status = 'active' LIMIT 20000"

        val actual = DatabaseExecutor.buildPagedSql("mysql", baseSql, offset, pageSize)

        assertEquals(expected, actual)
    }
}
