package com.datayes.utils

import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.classic.spi.IThrowableProxy
import ch.qos.logback.core.AppenderBase
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.stereotype.Controller
import java.text.SimpleDateFormat
import java.util.Date
import java.util.concurrent.ConcurrentLinkedQueue
import kotlin.collections.filter
import kotlin.collections.firstOrNull
import kotlin.collections.joinToString
import kotlin.collections.takeLast
import kotlin.collections.toList
import kotlin.let
import kotlin.ranges.coerceAtMost
import kotlin.text.contains
import kotlin.text.format
import kotlin.text.isEmpty
import kotlin.text.replace
import kotlin.text.substringAfterLast
import kotlin.to

/**
 * 日志页面控制器，提供日志页面的访问
 */
@Controller
class LogPageController {

    /**
     * 提供日志页面访问
     * @return 日志页面模板名
     */
    @GetMapping("/logs")
    fun logsPage(): String {
        return "logs"
    }
}

/**
 * 日志 API 控制器，提供访问内存日志的端点
 */
@RestController
class LogController {

    /**
     * 获取内存中存储的日志
     * @param limit 要返回的日志条目数量
     * @return 日志条目列表
     */
    @GetMapping("/api/logs")
    fun getLogs(@RequestParam(defaultValue = "100") limit: Int): List<String> {
        return InMemoryLogStore.getLogs(limit)
    }

    /**
     * 清除内存中的日志
     * @return 操作结果消息
     */
    @GetMapping("/api/logs/clear")
    fun clearLogs(): Map<String, String> {
        InMemoryLogStore.clearLogs()
        return mapOf("message" to "日志已清除")
    }

    /**
     * 搜索日志接口
     * @param query 搜索关键词
     * @param limit 要返回的日志条目数量
     * @return 符合搜索条件的日志条目列表
     */
    @GetMapping("/api/logs/search")
    fun searchLogs(
        @RequestParam(defaultValue = "") query: String,
        @RequestParam(defaultValue = "100") limit: Int,
    ): List<String> {
        return InMemoryLogStore.getLogs(limit)
            .filter { it.contains(query, ignoreCase = true) }
    }

    /**
     * 返回日志的HTML片段
     * @param query 搜索关键词
     * @param limit 要返回的日志条目数量
     * @return HTML格式的日志片段
     */
    @GetMapping("/logs/fragment", produces = ["text/html"])
    fun logsFragment(
        @RequestParam(defaultValue = "") query: String,
        @RequestParam(defaultValue = "100") limit: Int,
    ): String {
        val logs = if (query.isEmpty()) {
            InMemoryLogStore.getLogs(limit)
        } else {
            InMemoryLogStore.getLogs(limit)
                .filter { it.contains(query, ignoreCase = true) }
        }
        return logs.joinToString("") {
            "<div class='log-entry'>${it.replace("\n", "<br/>").replace(" ", "&nbsp;")}</div>"
        }
    }
}

/**
 * 一个单例对象，用于在内存中存储最近的日志条目。
 * 使用 ConcurrentLinkedQueue 保证线程安全。
 */
object InMemoryLogStore {

    // 使用线程安全的队列来存储格式化后的日志字符串
    private val logQueue = ConcurrentLinkedQueue<String>()

    // 限制存储的最大日志条目数，可根据需要调整
    private const val DEFAULT_MAX_SIZE = 1000
    private var maxSize = DEFAULT_MAX_SIZE // 允许后续通过配置修改 (如果需要)

    /**
     * 添加一条日志条目到队列。
     * 如果队列已满，则移除最旧的条目。
     *
     * @param logEntry 格式化后的日志字符串。
     */
    fun appendLog(logEntry: String) {
        // 在添加前检查大小，确保不会超过 maxSize
        while (logQueue.size >= maxSize) {
            logQueue.poll() // 移除队列头部的元素 (最旧的)
        }
        logQueue.add(logEntry) // 在队列尾部添加新元素
    }

    /**
     * 获取存储的最新日志条目。
     *
     * @param limit 要获取的最大日志条目数，默认为队列的最大容量。
     * @return 最近的日志条目列表。
     */
    fun getLogs(limit: Int = maxSize): List<String> {
        // 使用 takeLast 获取队列尾部的元素 (最新的)
        // 注意：对于 ConcurrentLinkedQueue，直接迭代或 toList() 可能更高效
        // 但 takeLast 对于获取最新的 N 个元素是清晰的意图表达
        // 对于非常大的队列和频繁调用，考虑性能影响
        return logQueue.toList().takeLast(limit.coerceAtMost(maxSize)) // 转为列表后取最后 N 个
    }

    /**
     * (可选) 允许设置最大队列大小。
     * 如果需要通过配置设置，可以添加此方法。
     */
    fun setMaxSize(newSize: Int) {
        if (newSize > 0) {
            maxSize = newSize
            // (可选) 如果新大小小于当前大小，可能需要立即清理超出部分
            while (logQueue.size > maxSize) {
                logQueue.poll()
            }
        }
    }

    /**
     * (可选) 清除所有日志。
     */
    fun clearLogs() {
        logQueue.clear()
    }
}

/**
 * SLF4J/Logback 的内存日志 Appender
 * 将日志记录到 InMemoryLogStore 中，便于通过 API 查询
 */
class InMemoryLogAppender : AppenderBase<ILoggingEvent>() {

    private val dateFormat = SimpleDateFormat("HH:mm:ss.SSS")

    override fun append(event: ILoggingEvent) {
        try {
            // 格式化日志
            val formattedLog = formatLogEvent(event)

            // 将格式化后的日志添加到 InMemoryLogStore
            InMemoryLogStore.appendLog(formattedLog)
        } catch (e: Exception) {
            // 防止日志处理过程中的异常影响主应用
            System.err.println("Error in InMemoryLogAppender: ${e.message}")
        }
    }

    /**
     * 格式化日志事件，匹配应用中配置的日志格式
     * 格式: %d{HH:mm:ss} %-5p [%c{1}.%M](%F:%L) %s%e%n
     */
    private fun formatLogEvent(event: ILoggingEvent): String {
        val date = dateFormat.format(Date(event.timeStamp))

        // 获取日志级别并左对齐到5个字符
        val level = String.format("%-5s", event.level.toString())

        // 获取类名的最后一部分(简短类名)
        val loggerName = event.loggerName
        val className = loggerName.substringAfterLast('.')

        // 获取方法名 (如果可用)
        val methodName = event.callerData.firstOrNull()?.methodName ?: "Unknown"

        // 获取源文件名和行号 (如果可用)
        val sourceFile = event.callerData.firstOrNull()?.let {
            it.fileName
        } ?: "Unknown"
        val lineNumber = event.callerData.firstOrNull()?.lineNumber ?: 0

        // 构建完整的日志消息
        val message = event.formattedMessage

        // 异常信息处理 - 包含完整堆栈跟踪
        val throwable = if (event.throwableProxy != null) {
            "\n" + formatThrowable(event.throwableProxy)
        } else {
            ""
        }

        // 返回格式化的日志字符串
        return "$date $level [$className.$methodName]($sourceFile:$lineNumber) $message$throwable\n"
    }

    /**
     * 格式化异常及其堆栈跟踪
     * @param throwableProxy 异常代理对象
     * @return 格式化后的异常信息，包含完整堆栈跟踪
     */
    private fun formatThrowable(throwableProxy: ch.qos.logback.classic.spi.IThrowableProxy): String {
        val sb = StringBuilder()
        
        // 添加异常类名和消息
        sb.append(throwableProxy.className)
        if (throwableProxy.message != null) {
            sb.append(": ").append(throwableProxy.message)
        }
        sb.append("\n")
        
        // 添加堆栈跟踪元素
        throwableProxy.stackTraceElementProxyArray?.forEach { steProxy ->
            sb.append("\tat ").append(steProxy.toString()).append("\n")
        }
        
        // 处理嵌套的 caused by 异常
        var cause = throwableProxy.cause
        while (cause != null) {
            sb.append("Caused by: ").append(cause.className)
            if (cause.message != null) {
                sb.append(": ").append(cause.message)
            }
            sb.append("\n")
            
            cause.stackTraceElementProxyArray?.forEach { steProxy ->
                sb.append("\tat ").append(steProxy.toString()).append("\n")
            }
            
            cause = cause.cause
        }
        
        // 处理 suppressed 异常
        throwableProxy.suppressed?.forEach { suppressedProxy ->
            sb.append("Suppressed: ").append(suppressedProxy.className)
            if (suppressedProxy.message != null) {
                sb.append(": ").append(suppressedProxy.message)
            }
            sb.append("\n")
            
            suppressedProxy.stackTraceElementProxyArray?.forEach { steProxy ->
                sb.append("\tat ").append(steProxy.toString()).append("\n")
            }
        }
        
        return sb.toString().trimEnd()
    }
}
