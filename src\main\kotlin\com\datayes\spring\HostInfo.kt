package com.datayes.spring

import org.springframework.stereotype.Component
import java.net.InetAddress

/**
 * Provides information about the host running the application instance.
 * This is a placeholder; you might need a more sophisticated implementation
 * depending on your deployment environment (e.g., Kubernetes pod name, instance ID).
 */
@Component
class HostInfo {

    val hostIdentifier: String = try {
        InetAddress.getLocalHost().hostName
    } catch (e: Exception) {
        "unknown-host"
    }

    fun getHostIp(): String {
        return try {
            InetAddress.getLocalHost().hostAddress
        } catch (e: Exception) {
            "unknown-ip"
        }
    }
}
