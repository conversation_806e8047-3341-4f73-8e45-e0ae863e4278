package com.datayes.executor

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class Md5Test {

    @Test
    fun `md5Hex should return correct md5 hash`() {
        val input = "hello world"
        val expected = "5eb63bbbe01eeed093cb22bb8f5acdc3"  // 预期MD5

        val actual = DatabaseExecutor.md5Hex(input)

        assertEquals(expected, actual)
    }

    @Test
    fun `md5Hex of empty string`() {
        val input = ""
        val expected = "d41d8cd98f00b204e9800998ecf8427e"  // 空字符串MD5

        val actual = DatabaseExecutor.md5Hex(input)

        assertEquals(expected, actual)
    }

    @Test
    fun `md5Hex called twice returns same result`() {
        val input = "some test string"

        val first = DatabaseExecutor.md5Hex(input)
        val second = DatabaseExecutor.md5Hex(input)

        assertEquals(first, second)
    }

    @Test
    fun `md5Hex of json string`() {
        val json = """{"name":"张三","age":30,"active":true}"""
        val expected = DatabaseExecutor.md5Hex(json)

        val actual = DatabaseExecutor.md5Hex(json)

        assertEquals(expected, actual)
    }
}
