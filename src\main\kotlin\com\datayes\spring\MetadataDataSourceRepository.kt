package com.datayes.spring

import com.datayes.domain.MetadataDataSourceEntity
import org.springframework.data.repository.ListCrudRepository
import org.springframework.stereotype.Repository

@Repository
interface MetadataDataSourceRepository : ListCrudRepository<MetadataDataSourceEntity, Long> {

    /**
     * 根据数据源名称查询数据源
     */
    fun findBySourceName(sourceName: String): MetadataDataSourceEntity?

    /**
     * 根据数据库类型查询数据源列表
     */
    fun findByDbType(dbType: String): List<MetadataDataSourceEntity>

    /**
     * 查询所有有效的数据源
     */
    fun findByActiveFlag(activeFlag: Boolean): List<MetadataDataSourceEntity>

    /**
     * 根据数据源名称和数据库类型查询数据源
     */
    fun findBySourceNameAndDbType(sourceName: String, dbType: String): MetadataDataSourceEntity?
} 