package com.datayes.spring

/**
 * 标准响应格式 (Standard Response Format)
 * 用于统一API响应的数据结构
 */
data class StandardResponse<T>(
    val code: Int,
    val message: String,
    val data: T?
) {
    companion object {
        /**
         * 创建成功响应 (Create Success Response)
         */
        fun <T> success(data: T?, message: String = "操作成功 (Operation successful)"): StandardResponse<T> =
            StandardResponse(200, message, data)

        /**
         * 创建错误响应 (Create Error Response)
         */
        fun <T> error(message: String, code: Int = 500): StandardResponse<T> =
            StandardResponse(code, message, null)

        /**
         * 创建验证失败响应 (Create Validation Failure Response)
         */
        fun <T> validationFailed(message: String): StandardResponse<T> =
            StandardResponse(400, message, null)
    }
}